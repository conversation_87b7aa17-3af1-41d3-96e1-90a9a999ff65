<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\TransaksiPenjualan;
use App\Models\User;
use App\Models\NotificationSetting;
use App\Filament\Resources\TransaksiPenjualanResource\Pages\ViewTransaksiPenjualan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;

class TransaksiPenjualanApprovalAuthorizationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->authorizedUser = User::factory()->create([
            'name' => 'Authorized Manager',
            'email' => '<EMAIL>',
            'hp' => '6285274897212'
        ]);
        
        $this->unauthorizedUser = User::factory()->create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'hp' => '6285274897213'
        ]);
        
        // Create notification setting for authorized user
        NotificationSetting::create([
            'event_name' => 'penjualan_baru',
            'user_id' => $this->authorizedUser->id,
            'channel' => 'whatsapp',
            'is_active' => true,
        ]);
    }

    public function test_authorized_user_can_see_approval_button()
    {
        // Login as authorized user
        Auth::login($this->authorizedUser);
        
        // Create pending transaction
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'pending_approval',
            'created_by' => $this->unauthorizedUser->id
        ]);

        // Create ViewTransaksiPenjualan instance
        $viewPage = new ViewTransaksiPenjualan();
        $viewPage->record = $transaction;
        
        // Test isCurrentUserAuthorizedApprover method
        $reflection = new \ReflectionClass($viewPage);
        $method = $reflection->getMethod('isCurrentUserAuthorizedApprover');
        $method->setAccessible(true);
        
        $isAuthorized = $method->invoke($viewPage);
        
        $this->assertTrue($isAuthorized, 'Authorized user should be able to approve');
    }

    public function test_unauthorized_user_cannot_see_approval_button()
    {
        // Login as unauthorized user
        Auth::login($this->unauthorizedUser);
        
        // Create pending transaction
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'pending_approval',
            'created_by' => $this->authorizedUser->id
        ]);

        // Create ViewTransaksiPenjualan instance
        $viewPage = new ViewTransaksiPenjualan();
        $viewPage->record = $transaction;
        
        // Test isCurrentUserAuthorizedApprover method
        $reflection = new \ReflectionClass($viewPage);
        $method = $reflection->getMethod('isCurrentUserAuthorizedApprover');
        $method->setAccessible(true);
        
        $isAuthorized = $method->invoke($viewPage);
        
        $this->assertFalse($isAuthorized, 'Unauthorized user should not be able to approve');
    }

    public function test_notification_setting_inactive_prevents_authorization()
    {
        // Deactivate notification setting
        NotificationSetting::where('user_id', $this->authorizedUser->id)
            ->update(['is_active' => false]);
        
        // Login as previously authorized user
        Auth::login($this->authorizedUser);
        
        // Create pending transaction
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'pending_approval',
            'created_by' => $this->unauthorizedUser->id
        ]);

        // Create ViewTransaksiPenjualan instance
        $viewPage = new ViewTransaksiPenjualan();
        $viewPage->record = $transaction;
        
        // Test isCurrentUserAuthorizedApprover method
        $reflection = new \ReflectionClass($viewPage);
        $method = $reflection->getMethod('isCurrentUserAuthorizedApprover');
        $method->setAccessible(true);
        
        $isAuthorized = $method->invoke($viewPage);
        
        $this->assertFalse($isAuthorized, 'User with inactive notification setting should not be able to approve');
    }

    public function test_different_event_name_prevents_authorization()
    {
        // Update notification setting to different event
        NotificationSetting::where('user_id', $this->authorizedUser->id)
            ->update(['event_name' => 'different_event']);
        
        // Login as user
        Auth::login($this->authorizedUser);
        
        // Create pending transaction
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'pending_approval',
            'created_by' => $this->unauthorizedUser->id
        ]);

        // Create ViewTransaksiPenjualan instance
        $viewPage = new ViewTransaksiPenjualan();
        $viewPage->record = $transaction;
        
        // Test isCurrentUserAuthorizedApprover method
        $reflection = new \ReflectionClass($viewPage);
        $method = $reflection->getMethod('isCurrentUserAuthorizedApprover');
        $method->setAccessible(true);
        
        $isAuthorized = $method->invoke($viewPage);
        
        $this->assertFalse($isAuthorized, 'User with different event notification setting should not be able to approve');
    }

    public function test_multiple_authorized_users()
    {
        // Create another authorized user
        $anotherAuthorizedUser = User::factory()->create([
            'name' => 'Another Manager',
            'email' => '<EMAIL>',
            'hp' => '6285274897214'
        ]);
        
        // Create notification setting for second user
        NotificationSetting::create([
            'event_name' => 'penjualan_baru',
            'user_id' => $anotherAuthorizedUser->id,
            'channel' => 'whatsapp',
            'is_active' => true,
        ]);
        
        // Test first user
        Auth::login($this->authorizedUser);
        
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'pending_approval',
            'created_by' => $this->unauthorizedUser->id
        ]);

        $viewPage = new ViewTransaksiPenjualan();
        $viewPage->record = $transaction;
        
        $reflection = new \ReflectionClass($viewPage);
        $method = $reflection->getMethod('isCurrentUserAuthorizedApprover');
        $method->setAccessible(true);
        
        $isAuthorized1 = $method->invoke($viewPage);
        
        // Test second user
        Auth::login($anotherAuthorizedUser);
        $isAuthorized2 = $method->invoke($viewPage);
        
        $this->assertTrue($isAuthorized1, 'First authorized user should be able to approve');
        $this->assertTrue($isAuthorized2, 'Second authorized user should be able to approve');
    }

    public function test_guest_user_cannot_approve()
    {
        // Logout (guest user)
        Auth::logout();
        
        // Create pending transaction
        $transaction = TransaksiPenjualan::factory()->create([
            'status' => 'pending_approval',
            'created_by' => $this->authorizedUser->id
        ]);

        // Create ViewTransaksiPenjualan instance
        $viewPage = new ViewTransaksiPenjualan();
        $viewPage->record = $transaction;
        
        // Test isCurrentUserAuthorizedApprover method
        $reflection = new \ReflectionClass($viewPage);
        $method = $reflection->getMethod('isCurrentUserAuthorizedApprover');
        $method->setAccessible(true);
        
        $isAuthorized = $method->invoke($viewPage);
        
        $this->assertFalse($isAuthorized, 'Guest user should not be able to approve');
    }
}
