<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\TransaksiPenjualan;
use App\Filament\Resources\TransaksiPenjualanResource;

class TransaksiPenjualanApprovalUnitTest extends TestCase
{
    public function test_can_edit_method_returns_false_for_approved_transaction()
    {
        // Create a mock transaction with approved status
        $transaction = new TransaksiPenjualan();
        $transaction->status = 'approved';

        // Test canEdit method
        $canEdit = TransaksiPenjualanResource::canEdit($transaction);
        
        $this->assertFalse($canEdit, 'Approved transaction should not be editable');
    }

    public function test_can_edit_method_returns_true_for_pending_transaction()
    {
        // Create a mock transaction with pending status
        $transaction = new TransaksiPenjualan();
        $transaction->status = 'pending_approval';

        // Test canEdit method
        $canEdit = TransaksiPenjualanResource::canEdit($transaction);
        
        $this->assertTrue($canEdit, 'Pending transaction should be editable');
    }

    public function test_can_edit_method_returns_true_for_rejected_transaction()
    {
        // Create a mock transaction with rejected status
        $transaction = new TransaksiPenjualan();
        $transaction->status = 'rejected';

        // Test canEdit method
        $canEdit = TransaksiPenjualanResource::canEdit($transaction);
        
        $this->assertTrue($canEdit, 'Rejected transaction should be editable');
    }

    public function test_can_edit_method_returns_true_for_needs_revision_transaction()
    {
        // Create a mock transaction with needs revision status
        $transaction = new TransaksiPenjualan();
        $transaction->status = 'needs_revision';

        // Test canEdit method
        $canEdit = TransaksiPenjualanResource::canEdit($transaction);
        
        $this->assertTrue($canEdit, 'Transaction needing revision should be editable');
    }

    public function test_can_edit_method_returns_true_for_non_transaction_record()
    {
        // Test with non-TransaksiPenjualan object
        $nonTransaction = new \stdClass();

        // Test canEdit method
        $canEdit = TransaksiPenjualanResource::canEdit($nonTransaction);
        
        $this->assertTrue($canEdit, 'Non-transaction records should be editable by default');
    }

    public function test_can_edit_method_returns_true_for_null_status()
    {
        // Create a mock transaction with null status
        $transaction = new TransaksiPenjualan();
        $transaction->status = null;

        // Test canEdit method
        $canEdit = TransaksiPenjualanResource::canEdit($transaction);
        
        $this->assertTrue($canEdit, 'Transaction with null status should be editable');
    }

    public function test_can_edit_method_returns_true_for_unknown_status()
    {
        // Create a mock transaction with unknown status
        $transaction = new TransaksiPenjualan();
        $transaction->status = 'unknown_status';

        // Test canEdit method
        $canEdit = TransaksiPenjualanResource::canEdit($transaction);
        
        $this->assertTrue($canEdit, 'Transaction with unknown status should be editable');
    }
}
