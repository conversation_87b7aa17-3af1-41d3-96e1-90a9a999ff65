<?php

namespace Tests\Feature;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Receipt;
use App\Models\DeliveryOrder;
use App\Models\TransaksiPenjualan;
use App\Models\Pelanggan;
use App\Models\AlamatPelanggan;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Barryvdh\DomPDF\Facade\Pdf;

class ReceiptPdfEnhancementsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->actingAs(User::factory()->create([
            'role' => 'admin'
        ]));
    }

    /** @test */
    public function receipt_pdf_displays_dagang_transaction_details_correctly()
    {
        // Create test data for dagang transaction
        $pelanggan = Pelanggan::factory()->create([
            'nama' => 'PT. Test Dagang Customer'
        ]);

        $alamatPelanggan = AlamatPelanggan::factory()->create([
            'id_pelanggan' => $pelanggan->id,
            'alamat' => 'Jl. Test Dagang No. 123, Jakarta'
        ]);

        $transaksi = TransaksiPenjualan::factory()->create([
            'kode' => 'TRX-DAGANG-001',
            'tipe' => 'dagang',
            'id_pelanggan' => $pelanggan->id,
            'id_alamat_pelanggan' => $alamatPelanggan->id
        ]);

        $deliveryOrder = DeliveryOrder::factory()->create([
            'id_transaksi' => $transaksi->id,
            'volume_do' => 5000
        ]);

        $invoice = Invoice::factory()->create([
            'nomor_invoice' => 'INV-DAGANG-001',
            'id_transaksi' => $transaksi->id,
            'total_invoice' => 100000000
        ]);

        // Create invoice items
        InvoiceItem::factory()->create([
            'invoice_id' => $invoice->id,
            'item_name' => 'BBM Biosolar B30',
            'item_description' => 'Biosolar B30 Industri',
            'quantity' => 5000,
            'unit' => 'liter',
            'unit_price' => 20000,
            'subtotal' => 100000000
        ]);

        $receipt = Receipt::factory()->create([
            'nomor_receipt' => 'RCP-DAGANG-001',
            'id_invoice' => $invoice->id,
            'id_transaksi' => $transaksi->id,
            'jumlah_pembayaran' => 100000000
        ]);

        // Load receipt with relationships
        $receipt = Receipt::with([
            'invoice.transaksiPenjualan.pelanggan',
            'invoice.transaksiPenjualan.alamatPelanggan',
            'invoice.transaksiPenjualan.deliveryOrders',
            'invoice.invoiceItems',
            'transaksiPenjualan.pelanggan',
            'transaksiPenjualan.alamatPelanggan',
            'transaksiPenjualan.deliveryOrders'
        ])->find($receipt->id);

        $view = view('pdf.receipt', ['record' => $receipt, 'logoBase64' => '']);
        $html = $view->render();
        
        // Verify dagang transaction content
        $this->assertStringContainsString('pengiriman', $html);
        $this->assertStringContainsString('5.000 liter BBM', $html);
        $this->assertStringContainsString('BBM Biosolar B30', $html);
        $this->assertStringContainsString('Jl. Test Dagang No. 123, Jakarta', $html);
        $this->assertStringContainsString('INV-DAGANG-001', $html);
    }

    /** @test */
    public function receipt_pdf_displays_jasa_transaction_details_correctly()
    {
        // Create test data for jasa transaction
        $pelanggan = Pelanggan::factory()->create([
            'nama' => 'PT. Test Jasa Customer'
        ]);

        $alamatPelanggan = AlamatPelanggan::factory()->create([
            'id_pelanggan' => $pelanggan->id,
            'alamat' => 'Jl. Test Jasa No. 456, Pekanbaru'
        ]);

        $transaksi = TransaksiPenjualan::factory()->create([
            'kode' => 'TRX-JASA-001',
            'tipe' => 'jasa',
            'id_pelanggan' => $pelanggan->id,
            'id_alamat_pelanggan' => $alamatPelanggan->id
        ]);

        $invoice = Invoice::factory()->create([
            'nomor_invoice' => 'INV-JASA-001',
            'id_transaksi' => $transaksi->id,
            'total_invoice' => 50000000
        ]);

        // Create invoice items for service
        InvoiceItem::factory()->create([
            'invoice_id' => $invoice->id,
            'item_name' => 'Jasa Pengiriman BBM',
            'item_description' => 'Layanan pengiriman BBM ke lokasi customer',
            'quantity' => 1,
            'unit' => 'paket',
            'unit_price' => 50000000,
            'subtotal' => 50000000
        ]);

        $receipt = Receipt::factory()->create([
            'nomor_receipt' => 'RCP-JASA-001',
            'id_invoice' => $invoice->id,
            'id_transaksi' => $transaksi->id,
            'jumlah_pembayaran' => 50000000
        ]);

        // Load receipt with relationships
        $receipt = Receipt::with([
            'invoice.transaksiPenjualan.pelanggan',
            'invoice.transaksiPenjualan.alamatPelanggan',
            'invoice.transaksiPenjualan.deliveryOrders',
            'invoice.invoiceItems',
            'transaksiPenjualan.pelanggan',
            'transaksiPenjualan.alamatPelanggan',
            'transaksiPenjualan.deliveryOrders'
        ])->find($receipt->id);

        $view = view('pdf.receipt', ['record' => $receipt, 'logoBase64' => '']);
        $html = $view->render();
        
        // Verify jasa transaction content
        $this->assertStringContainsString('layanan', $html);
        $this->assertStringContainsString('Jasa Pengiriman BBM', $html);
        $this->assertStringContainsString('Jl. Test Jasa No. 456, Pekanbaru', $html);
        $this->assertStringContainsString('INV-JASA-001', $html);
    }

    /** @test */
    public function receipt_pdf_english_version_works_correctly()
    {
        // Create test data
        $pelanggan = Pelanggan::factory()->create([
            'nama' => 'PT. Test English Customer'
        ]);

        $alamatPelanggan = AlamatPelanggan::factory()->create([
            'id_pelanggan' => $pelanggan->id,
            'alamat' => 'Test English Address 789, Batam'
        ]);

        $transaksi = TransaksiPenjualan::factory()->create([
            'kode' => 'TRX-EN-001',
            'tipe' => 'dagang',
            'id_pelanggan' => $pelanggan->id,
            'id_alamat_pelanggan' => $alamatPelanggan->id
        ]);

        $deliveryOrder = DeliveryOrder::factory()->create([
            'id_transaksi' => $transaksi->id,
            'volume_do' => 3000
        ]);

        $invoice = Invoice::factory()->create([
            'nomor_invoice' => 'INV-EN-001',
            'id_transaksi' => $transaksi->id,
            'total_invoice' => 75000000
        ]);

        InvoiceItem::factory()->create([
            'invoice_id' => $invoice->id,
            'item_name' => 'Industrial Diesel Fuel',
            'quantity' => 3000,
            'unit' => 'liters',
            'unit_price' => 25000,
            'subtotal' => 75000000
        ]);

        $receipt = Receipt::factory()->create([
            'nomor_receipt' => 'RCP-EN-001',
            'id_invoice' => $invoice->id,
            'id_transaksi' => $transaksi->id,
            'jumlah_pembayaran' => 75000000
        ]);

        // Load receipt with relationships
        $receipt = Receipt::with([
            'invoice.transaksiPenjualan.pelanggan',
            'invoice.transaksiPenjualan.alamatPelanggan',
            'invoice.transaksiPenjualan.deliveryOrders',
            'invoice.invoiceItems'
        ])->find($receipt->id);

        $view = view('pdf.receipt_en', ['record' => $receipt, 'logoBase64' => '']);
        $html = $view->render();
        
        // Verify English content
        $this->assertStringContainsString('Payment for Invoice', $html);
        $this->assertStringContainsString('delivery of', $html);
        $this->assertStringContainsString('3,000 liters BBM', $html);
        $this->assertStringContainsString('Industrial Diesel Fuel', $html);
        $this->assertStringContainsString('Test English Address 789, Batam', $html);
        $this->assertStringContainsString('INV-EN-001', $html);
    }

    /** @test */
    public function receipt_pdf_handles_missing_invoice_items_gracefully()
    {
        // Create receipt without invoice items
        $transaksi = TransaksiPenjualan::factory()->create([
            'tipe' => 'dagang'
        ]);

        $invoice = Invoice::factory()->create([
            'id_transaksi' => $transaksi->id,
            'total_invoice' => 25000000
        ]);

        $receipt = Receipt::factory()->create([
            'id_invoice' => $invoice->id,
            'id_transaksi' => $transaksi->id,
            'jumlah_pembayaran' => 25000000
        ]);

        // Load receipt with relationships
        $receipt = Receipt::with([
            'invoice.transaksiPenjualan',
            'invoice.invoiceItems',
            'transaksiPenjualan'
        ])->find($receipt->id);

        $view = view('pdf.receipt', ['record' => $receipt, 'logoBase64' => '']);
        $html = $view->render();
        
        // Should still render without errors and show fallback content
        $this->assertNotEmpty($html);
        $this->assertStringContainsString('BBM', $html); // Fallback content
        $this->assertStringContainsString('pengiriman', $html);
    }

    /** @test */
    public function receipt_pdf_template_renders_without_errors()
    {
        $receipt = Receipt::factory()->create([
            'nomor_receipt' => 'RCP-TEST-001',
            'jumlah_pembayaran' => 100000000
        ]);

        $view = view('pdf.receipt', ['record' => $receipt, 'logoBase64' => '']);
        $html = $view->render();
        
        $this->assertNotEmpty($html);
        $this->assertStringContainsString('KWITANSI', $html);
        $this->assertStringContainsString('RCP-TEST-001', $html);
        $this->assertStringContainsString('PT. LINTAS RIAU PRIMA', $html);
    }
}
