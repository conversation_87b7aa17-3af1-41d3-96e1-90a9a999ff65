{{--
    Receipt Preview Template
    Location: resources/views/receipt/receipt-preview.blade.php
--}}

<div class="p-6 bg-white font-sans text-gray-800 max-w-4xl mx-auto">
    {{-- Header Section --}}
    <div class="flex justify-between items-start mb-8">
        {{-- Company Info --}}
        <div class="flex-1">
            <h1 class="text-2xl font-bold text-blue-800 mb-2">PT. LINTAS RIAU PRIMA</h1>
            <div class="text-sm text-gray-600 space-y-1">
                <p>Jl. Raya Pekanbaru - Bangkinang KM 25</p>
                <p>Desa Rimbo Panjang, Kec. Tambang</p>
                <p>Kabupaten Kampar, Riau 28293</p>
                <p>Telp: (0761) 7051234 | Email: <EMAIL></p>
                <p>NPWP: 01.234.567.8-901.000</p>
            </div>
        </div>

        {{-- Receipt Title --}}
        <div class="text-right">
            <h2 class="text-3xl font-bold text-green-600 mb-2">KWITANSI</h2>
            <div class="text-sm">
                <p><strong>No. Kwitansi:</strong> {{ $record->nomor_receipt }}</p>
                <p><strong>Tanggal:</strong> {{ $record->tanggal_receipt->format('d F Y') }}</p>
                @if($record->tanggal_pembayaran)
                    <p><strong>Tgl Pembayaran:</strong> {{ $record->tanggal_pembayaran->format('d F Y') }}</p>
                @endif
            </div>
        </div>
    </div>

    {{-- Customer Information --}}
    <div class="mb-8">
        <h3 class="text-lg font-semibold mb-3 text-gray-800 border-b border-gray-300 pb-1">DITERIMA DARI:</h3>
        <div class="bg-gray-50 p-4 rounded">
            @if($record->invoice)
                <p class="font-semibold text-lg">{{ $record->invoice->nama_pelanggan }}</p>
                <div class="text-sm text-gray-600 mt-2 whitespace-pre-line">{{ $record->invoice->alamat_pelanggan }}</div>
                @if($record->invoice->npwp_pelanggan)
                    <p class="text-sm mt-2"><strong>NPWP:</strong> {{ $record->invoice->npwp_pelanggan }}</p>
                @endif
            @elseif($record->transaksiPenjualan && $record->transaksiPenjualan->pelanggan)
                <p class="font-semibold text-lg">{{ $record->transaksiPenjualan->pelanggan->nama }}</p>
                @if($record->transaksiPenjualan->pelanggan->alamatUtama)
                    <div class="text-sm text-gray-600 mt-2">
                        {{ $record->transaksiPenjualan->pelanggan->alamatUtama->alamat_lengkap }}
                    </div>
                @endif
            @else
                <p class="text-gray-500">Informasi pelanggan tidak tersedia</p>
            @endif
        </div>
    </div>

    {{-- Payment Information --}}
    <div class="mb-8">
        <h3 class="text-lg font-semibold mb-3 text-gray-800 border-b border-gray-300 pb-1">UNTUK PEMBAYARAN:</h3>
        <div class="bg-blue-50 p-4 rounded">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    @if($record->invoice)
                        <p><strong>No. Invoice:</strong> {{ $record->invoice->nomor_invoice }}</p>
                    @endif
                    @if($record->transaksiPenjualan)
                        <p><strong>No. SO:</strong> {{ $record->transaksiPenjualan->kode }}</p>
                    @endif
                    @if($record->deliveryOrder)
                        <p><strong>No. DO:</strong> {{ $record->deliveryOrder->kode }}</p>
                    @endif
                </div>
                <div>
                    <p><strong>Metode Pembayaran:</strong> {{ ucfirst(str_replace('_', ' ', $record->metode_pembayaran)) }}</p>
                    @if($record->referensi_pembayaran)
                        <p><strong>Referensi:</strong> {{ $record->referensi_pembayaran }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    {{-- Payment Details --}}
    <div class="mb-8">
        <div class="bg-green-50 border border-green-200 p-6 rounded-lg">
            <h3 class="text-xl font-bold text-green-800 mb-4 text-center">RINCIAN PEMBAYARAN</h3>
            
            <div class="space-y-3">
                <div class="flex justify-between items-center py-2 border-b border-green-200">
                    <span class="font-medium">Jumlah Pembayaran:</span>
                    <span class="text-lg font-semibold">Rp {{ number_format($record->jumlah_pembayaran, 0, ',', '.') }}</span>
                </div>
                
                @if($record->biaya_admin > 0)
                    <div class="flex justify-between items-center py-2 border-b border-green-200">
                        <span class="font-medium">Biaya Admin:</span>
                        <span class="text-lg">Rp {{ number_format($record->biaya_admin, 0, ',', '.') }}</span>
                    </div>
                @endif
                
                <div class="flex justify-between items-center py-3 border-t-2 border-green-400">
                    <span class="text-xl font-bold text-green-800">TOTAL DITERIMA:</span>
                    <span class="text-2xl font-bold text-green-800">Rp {{ number_format($record->total_diterima, 0, ',', '.') }}</span>
                </div>
            </div>
        </div>
    </div>

    {{-- Amount in Words --}}
    <div class="mb-8">
        <h4 class="font-semibold text-gray-700 mb-2">Terbilang:</h4>
        <div class="bg-gray-100 p-3 rounded border-l-4 border-blue-400">
            <p class="italic text-gray-800">
                {{ ucfirst(\App\Helpers\NumberToWords::convert($record->total_diterima)) }} Rupiah
            </p>
        </div>
    </div>

    {{-- Bank Information --}}
    @if($record->bank_pengirim || $record->bank_penerima)
        <div class="mb-8">
            <h4 class="font-semibold text-gray-700 mb-3">Informasi Bank:</h4>
            <div class="grid grid-cols-2 gap-4">
                @if($record->bank_pengirim)
                    <div class="bg-gray-50 p-3 rounded">
                        <h5 class="font-medium text-gray-700 mb-1">Bank Pengirim:</h5>
                        <p class="text-sm">{{ $record->bank_pengirim }}</p>
                    </div>
                @endif
                @if($record->bank_penerima)
                    <div class="bg-gray-50 p-3 rounded">
                        <h5 class="font-medium text-gray-700 mb-1">Bank Penerima:</h5>
                        <p class="text-sm">{{ $record->bank_penerima }}</p>
                    </div>
                @endif
            </div>
        </div>
    @endif

    {{-- Status --}}
    <div class="mb-6">
        <div class="flex justify-center">
            <div class="px-6 py-3 rounded-full text-white font-semibold text-lg
                @if($record->status === 'confirmed') bg-green-500
                @elseif($record->status === 'pending') bg-yellow-500
                @else bg-gray-500
                @endif">
                @if($record->status === 'confirmed')
                    ✅ PEMBAYARAN DIKONFIRMASI
                @elseif($record->status === 'pending')
                    ⏳ MENUNGGU KONFIRMASI
                @else
                    ❓ {{ strtoupper($record->status) }}
                @endif
            </div>
        </div>
    </div>

    {{-- Notes --}}
    @if($record->catatan)
        <div class="mb-8">
            <h4 class="font-semibold text-gray-700 mb-2">Catatan:</h4>
            <div class="bg-yellow-50 border border-yellow-200 p-3 rounded text-sm">
                {{ $record->catatan }}
            </div>
        </div>
    @endif

    {{-- Signature Section --}}
    <div class="mt-12 pt-6 border-t border-gray-300">
        <div class="flex justify-between">
            {{-- Received By --}}
            <div class="text-center w-1/3">
                <p class="text-sm mb-16">Yang Menerima,</p>
                <div class="border-b border-gray-400 mb-2"></div>
                <p class="text-sm">{{ $record->invoice->nama_pelanggan ?? 'Pelanggan' }}</p>
            </div>

            {{-- Issued By --}}
            <div class="text-center w-1/3">
                <p class="text-sm mb-16">Hormat kami,</p>
                <div class="border-b border-gray-400 mb-2"></div>
                <p class="text-sm font-semibold">{{ $record->createdBy->name ?? 'Finance Department' }}</p>
                <p class="text-xs text-gray-600">{{ $record->createdBy->jabatan->nama_jabatan ?? 'Finance Manager' }}</p>
            </div>
        </div>
    </div>

    {{-- Footer --}}
    <div class="mt-8 text-center">
        <div class="text-xs text-gray-500 space-y-1">
            <p>Kwitansi ini dibuat secara otomatis oleh sistem</p>
            <p>Tanggal cetak: {{ now()->format('d F Y H:i') }}</p>
            <p class="font-medium">PT. Lintas Riau Prima - Finance Department</p>
        </div>
    </div>
</div>
