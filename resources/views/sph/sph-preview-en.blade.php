<!-- include -->
@php
    use App\Support\Formatter;
@endphp

<!-- logic -->
@php
    $locale = 'en';

    // The $record variable is passed in.
    $letterSetting = $record->letterSetting;

    // Fetch ISO certifications (this logic remains)
    $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
    $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
        ->whereIn('name', $isoNamesToDisplay)
        ->get();

    // SIGNATURE SET PART - Use the new SignerService
    use App\Services\SignerService;
    $signer = SignerService::getDefaultSigner($record ?? null);
@endphp

<div class="p-4 sm:p-6 bg-white font-sans text-gray-800">
    {{-- Header Section --}}
    <header class="mb-8">
        <table class="w-full">
            <tbody>
                <tr>
                    <td class="w-1/3">
                        <img src="{{ asset('images/lrp.png') }}" alt="PT. Lintas Riau Prima" style="height: 150px;"
                            class="mb-2">
                    </td>
                    <td class="w-1/3"></td>
                    <td class="w-1/3 text-right">
                        <h2 class="font-bold text-lg">TRUSTED & RELIABLE PARTNER</h2>
                        <p class="text-xs">Fuel Agent – Fuel Transportation – Bunker Service</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </header>

    {{-- Document Info Section --}}
    <section class="mb-4">
        <div class="text-right text-sm font-semibold">
            {{ $letterSetting?->city }}, {{ Formatter::date($record->sph_date, $locale) }}
        </div>

        <table class="text-sm mt-4">
            <tbody>
                <tr>
                    <td class="pr-2">No.</td>
                    <td class="pr-2">:</td>
                    <td class="font-semibold">{{ $record->sph_number }}</td>
                </tr>
                <tr>
                    <td class="pr-2">Attachment</td>
                    <td class="pr-2">:</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td class="pr-2 align-top">Subject</td>
                    <td class="pr-2 align-top">:</td>
                    <td class="font-semibold">Price Quotation for Industrial Pertalite<br>Pertamina Patra Niaga</td>
                </tr>
            </tbody>
        </table>
    </section>

    {{-- Recipient Section --}}
    <section class="mb-4 text-sm font-semibold">
        <p>To:</p>
        <p class="font-bold">{{ $record->customer?->nama }}</p>
        @if ($record->opsional_pic)
            <p>Attn: {{ $record->opsional_pic }}</p>
        @elseif($record->customer?->pic_nama)
            <p>Attn: {{ $record->customer->pic_nama }}</p>
        @endif
        <p>At your location</p>
    </section>

    {{-- Body / Salutation --}}
    <section class="mb-6 text-sm leading-relaxed">
        <p class="mb-0">Dear Sir/Madam,</p>
        <p>
            In connection with the information on the need for industrial fuel, we hereby send a price quotation for the
            period
            <span class="font-bold">{{ Formatter::date($record->sph_date, $locale) }}</span> to <span
                class="font-bold">{{ Formatter::date($record->valid_until_date, $locale) }}</span>.
        </p>
    </section>

    {{-- Price Details Table --}}
    <section class="mb-2">
        <h2 class="text-sm font-semibold mb-2">The price quotation we provide is as follows:</h2>
        <table class="w-full text-left text-sm border-collapse border border-gray-400">
            <thead>
                <tr class="bg-gray-100">
                    <th class="p-2 border border-gray-300 text-center">No.</th>
                    <th class="p-2 border border-gray-300">Details</th>
                    <th class="p-2 border border-gray-300 text-right">Price/Liter</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($record->details as $idx => $detail)
                    <!-- harga dasar -->
                    <tr class="border-t border-gray-300">
                        <td class="p-2 border border-gray-300 text-center">{{ $idx + 1 }}</td>
                        <td class="p-2 border border-gray-300">Base Fuel Price</td>
                        <td class="p-2 border border-gray-300 text-right">
                            {{ Formatter::currency($detail->harga_dasar, $locale) }}</td>
                    </tr>

                    <!-- ppn/vat -->
                    @if ($detail->show_ppn)
                        <tr>
                            <td class="p-2 border border-gray-300 text-center"></td>
                            <td class="p-2 border border-gray-300">VAT 11%</td>
                            <td class="p-2 border border-gray-300 text-right">
                                {{ Formatter::currency($detail->ppn, $locale) }}</td>
                        </tr>
                    @endif

                    <!-- oat -->
                    @if ($detail->show_oat)
                        <tr>
                            <td class="p-2 border border-gray-300 text-center"></td>
                            <td class="p-2 border border-gray-300">Delivery Cost</td>
                            <td class="p-2 border border-gray-300 text-right">
                                {{ Formatter::currency($detail->oat, $locale) }}</td>
                        </tr>
                    @endif

                    <!-- pbbkb -->
                    @if ($detail->show_pbbkb)
                        <tr>
                            <td class="p-2 border border-gray-300 text-center"></td>
                            <td class="p-2 border border-gray-300">PBBKB</td>
                            <td class="p-2 border border-gray-300 text-right">
                                {{ Formatter::currency($detail->pbbkb, $locale) }}</td>
                        </tr>
                    @endif

                    <!-- total price -->
                    <tr class="font-bold bg-gray-100">
                        <td colspan="2" class="p-2 border border-gray-300">Total Quotation</td>
                        <td class="p-2 border border-gray-300 text-right">
                            {{ Formatter::currency($detail->price, $locale) }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </section>

    {{-- Terms and Conditions --}}
    <section class="mb-8 text-sm">
        <h2 class="font-semibold">Terms and Conditions</h2>
        <ol class="list-none space-y-0 mb-2">
            <li class="flex"><span class="mr-2">1.</span><span>This price quotation is valid for the period
                    {{ Formatter::date($record->sph_date, $locale) }} -
                    {{ Formatter::date($record->valid_until_date, $locale) }}.</span></li>
            <li class="flex"><span class="mr-2">2.</span>
                <div><span>Payment of CASH invoice after documents are received via transfer to Bank BNI</span>
                    <div class="font-semibold ml-4">Account No: ********* On behalf of PT. Lintas Riau Prima</div>
                </div>
            </li>
            <li class="flex"><span class="mr-2">3.</span><span>We receive the PO at least 3 (three) days (via email
                    or WA) before delivery.</span></li>
            <li class="flex"><span class="mr-2">4.</span><span>For urgent conditions, please coordinate directly
                    before 12:00 PM on the same day.</span></li>
        </ol>
        <span>Thus we convey this offer letter, and for your attention, we thank you very much.</span>
    </section>

    {{-- Signature Section --}}
    <section class="pt-8 flex justify-end mt-6 mb-6">
        <div class="text-center">
            <p class="text-sm">Sincerely,</p>
            <p class="text-sm">PT Lintas Riau Prima</p>

            @if ($signer)
                <x-signature-mounter :user="$signer" />
                <p class="text-sm font-bold underline">{{ $signer->name ?? 'No Name' }}</p>
                <p class="text-xs text-gray-600">{{ $signer->getPosition($locale) ?? 'No Position' }}</p>
            @else
                <div style="height: 100px;"></div>
                <p class="text-sm font-bold underline">N/A</p>
                <p class="text-xs text-gray-600">N/A</p>
            @endif
        </div>
    </section>

    {{-- Footer Section --}}
    <footer class="mt-16 pt-4 border-t-4 border-blue-800 flex justify-between items-center text-xs">
        <div class="flex items-center space-x-2">
            @if (isset($isoCertifications))
                @foreach ($isoCertifications as $cert)
                    <img src="{{ $cert->logo_url }}" alt="{{ $cert->name }}" class="h-10">
                @endforeach
            @endif
        </div>
        <div class="text-center">
            <p class="font-bold">PT. LINTAS RIAU PRIMA</p>
            <p>{{ $letterSetting?->address }}</p>
        </div>
        <div class="text-left">
            <p>☎️ 0761-22369</p>
            <p>✉️ <EMAIL></p>
            <p>🌐 www.lintasriauprima.com</p>
        </div>
    </footer>
</div>
