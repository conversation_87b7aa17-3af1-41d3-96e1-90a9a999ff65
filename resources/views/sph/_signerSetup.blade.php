{{--
    Reusable signer setup logic for SPH templates
    This partial determines who should sign the SPH document
    
    Usage: @include('sph._signerSetup', ['record' => $sph])
    
    After including this, you can use $signer variable in your template
--}}

@php
    use App\Services\SignerService;
    
    // Use the SignerService to determine the appropriate signer
    $signer = SignerService::getDefaultSigner($record ?? null);
@endphp
