<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Preview - {{ $record->nomor_invoice }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            color: #000;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            position: relative;
        }

        .company-section {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            flex: 1;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #1e40af, #fbbf24);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 10px;
            text-align: center;
            line-height: 1.1;
            flex-shrink: 0;
            overflow: hidden;
        }

        .company-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .company-logo-fallback {
            font-size: 9px;
            line-height: 1.1;
        }

        .company-info {
            flex: 1;
            margin-left: 10px;
        }

        .company-name {
            font-size: 16px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 3px;
        }

        .company-tagline {
            font-size: 12px;
            color: #000;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .company-services {
            font-size: 10px;
            color: #666;
            margin-bottom: 8px;
        }

        .header-right {
            width: 20px;
            height: 80px;
            background: linear-gradient(135deg, #1e40af, #fbbf24);
            border-radius: 4px;
        }

        .invoice-info {
            text-align: center;
            margin: 20px 0;
        }

        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            color: #000;
            margin-bottom: 5px;
            text-decoration: underline;
        }

        .invoice-number {
            font-size: 14px;
            color: #000;
            font-weight: bold;
        }

        /* Customer Details */
        .customer-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            margin-bottom: 3px;
            font-size: 11px;
        }

        .detail-label {
            width: 250px;
            flex-shrink: 0;
        }

        .detail-colon {
            width: 15px;
            flex-shrink: 0;
        }

        .detail-value {
            flex: 1;
        }

        /* Items Table */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 1px solid #000;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #000;
            padding: 8px 5px;
            text-align: center;
            font-size: 10px;
            vertical-align: top;
        }

        .items-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }

        .items-table .text-left {
            text-align: left;
        }

        .items-table .text-right {
            text-align: right;
        }

        .totals-row td {
            font-weight: bold;
            background-color: #f9f9f9;
        }

        .final-total-row td {
            font-weight: bold;
            background-color: #e0e0e0;
        }

        /* Terbilang Section */
        .terbilang-section {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #000;
            font-size: 11px;
        }

        /* Payment Notes */
        .payment-notes {
            margin: 15px 0;
            font-size: 10px;
            line-height: 1.4;
        }

        /* Signature Section */
        .signature-section {
            margin: 30px 0;
            text-align: center;
        }

        .signature-box {
            display: inline-block;
            text-align: center;
        }

        .signature-location {
            font-size: 11px;
            margin-bottom: 10px;
        }

        .signature-space {
            height: 60px;
            margin: 10px 0;
        }

        .signature-name {
            font-weight: bold;
            margin-top: 5px;
        }

        .signature-title {
            font-size: 10px;
        }

        /* Footer Section */
        .footer {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
            font-size: 10px;
        }

        .footer table {
            width: 100%;
            border-collapse: collapse;
        }

        .footer td {
            vertical-align: top;
            padding: 5px;
        }

        .footer .center {
            text-align: center;
        }

        .footer .right {
            text-align: right;
        }

        .footer p {
            margin: 2px 0;
            line-height: 1.3;
        }

        @media print {
            body {
                background-color: white;
                padding: 0;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div class="company-section">
                @php
                    $logoPath = public_path('images/lrp.png');
                    $logoBase64 = '';

                    if (File::exists($logoPath)) {
                        $logoBase64 = base64_encode(File::get($logoPath));
                    }
                @endphp
                <div class="company-logo {{ empty($logoBase64) ? 'company-logo-fallback' : '' }}">
                    @if (!empty($logoBase64))
                        <img src="data:image/png;base64,{{ $logoBase64 }}" alt="Company Logo">
                    @else
                        LINTAS<br>RIAU<br>PRIMA
                    @endif
                </div>
                <div class="company-info">
                    <div class="company-name">PT. LINTAS RIAU PRIMA</div>
                    <div class="company-tagline">TRUSTED & RELIABLE PARTNER</div>
                    <div class="company-services">Fuel Agent - Fuel Transportation - Bunker Services</div>
                    <div style="font-size: 10px; color: #666;">
                        @if ($record->letterSetting)
                            {{ $record->letterSetting->phone_number ?? '0761-22369' }} -
                            {{ $record->letterSetting->email ?? '<EMAIL>' }}<br>
                            {{ $record->letterSetting->website ?? 'www.lintasriauprima.com' }}
                        @else
                            0761-22369 - <EMAIL><br>
                            www.lintasriauprima.com
                        @endif
                    </div>
                </div>
            </div>
            <div class="header-right"></div>
        </div>

        <!-- Invoice Title Section -->
        <div class="invoice-info">
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-number">{{ $record->nomor_invoice }}</div>
        </div>

        <!-- Customer Details -->
        <div class="customer-details">
            <div style="margin-bottom: 15px;">
                <div class="detail-row">
                    <span class="detail-label">Customer Name</span>
                    <span class="detail-colon">:</span>
                    <span
                        class="detail-value">{{ $record->nama_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->nama ?? 'N/A') }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Customer Address</span>
                    <span class="detail-colon">:</span>
                    <span
                        class="detail-value">{{ $record->alamat_pelanggan ?? ($record->transaksiPenjualan?->pelanggan?->alamatUtama?->alamat ?? 'N/A') }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Pertamina Delivery Cover Letter No</span>
                    <span class="detail-colon">:</span>
                    <span class="detail-value">{{ $record->transaksiPenjualan?->kode ?? 'SO number' }}</span>
                </div>
                @php
                    $deliveryOrders = $record->transaksiPenjualan?->deliveryOrders ?? collect();
                    $doNumbers = $deliveryOrders->pluck('kode')->filter()->implode(', ');
                @endphp
                <div class="detail-row">
                    <span class="detail-label">Receipt No</span>
                    <span class="detail-colon">:</span>
                    <span class="detail-value">{{ $doNumbers ?: '15090, 15091' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Goods Delivery</span>
                    <span class="detail-colon">:</span>
                    <span class="detail-value"></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">PO No</span>
                    <span class="detail-colon">:</span>
                    <span class="detail-value">{{ $record->transaksiPenjualan?->nomor_po ?? '' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">PO Date</span>
                    <span class="detail-colon">:</span>
                    <span
                        class="detail-value">{{ $record->transaksiPenjualan?->tanggal ? $record->transaksiPenjualan->tanggal->format('Y-m-d') : '' }}</span>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">No</th>
                    <th style="width: 35%;">Description</th>
                    <th style="width: 15%;">Unit Price</th>
                    <th style="width: 15%;">Volume</th>
                    <th style="width: 15%;">VAT</th>
                    <th style="width: 15%;">Total</th>
                </tr>
            </thead>
            <tbody>
                @php
                    $itemNumber = 1;
                    $invoiceItems = $record->invoiceItems ?? collect();
                    $hasInvoiceItems = $invoiceItems->isNotEmpty();

                    // Calculate totals from invoice items
                    $totalSales = 0;
                    $totalTax = 0;
                    $subtotalCalculated = 0; // Initialize for fallback calculation
                @endphp

                @if ($hasInvoiceItems)
                    @foreach ($invoiceItems as $item)
                        {{-- Main item row --}}
                        <tr>
                            <td>{{ $itemNumber++ }}</td>
                            <td class="text-left">
                                {{ $item->item_name ?? ($item->item?->name ?? 'Item not found') }}<br>
                                <small
                                    style="color: #6b7280;">{{ $item->item_description ?? ($item->item?->description ?? '') }}</small>
                            </td>
                            <td class="text-right">${{ number_format($item->unit_price ?? 0, 0, ',', '.') }}</td>
                            <td class="text-right">{{ number_format($item->quantity ?? 0, 2, ',', '.') }}
                                {{ $item->unit ?? 'Liter' }}</td>
                            <td class="text-right">${{ number_format($item->ppn_amount ?? 0, 0, ',', '.') }}</td>
                            <td class="text-right">
                                ${{ number_format(($item->unit_price ?? 0) * ($item->quantity ?? 0), 0, ',', '.') }}
                            </td>
                        </tr>
                        @php
                            $totalSales += ($item->unit_price ?? 0) * ($item->quantity ?? 0);
                        @endphp

                        {{-- Operational cost row (if included) --}}
                        @if (($item->operasional_amount ?? 0) > 0)
                            <tr>
                                <td>{{ $itemNumber++ }}</td>
                                <td class="text-left">Operational Cost for
                                    {{ number_format($item->quantity ?? 0, 2, ',', '.') }} liters</td>
                                <td class="text-right">
                                    ${{ number_format(($item->operasional_amount ?? 0) / ($item->quantity ?? 1), 0, ',', '.') }}
                                </td>
                                <td class="text-right">{{ number_format($item->quantity ?? 0, 2, ',', '.') }}
                                    {{ $item->unit ?? 'Liter' }}</td>
                                <td class="text-right"></td>
                                <td class="text-right">
                                    ${{ number_format($item->operasional_amount ?? 0, 0, ',', '.') }}
                                </td>
                            </tr>
                            @php
                                $totalSales += $item->operasional_amount ?? 0;
                            @endphp
                        @endif

                        {{-- PBBKB cost row (if included) --}}
                        @if (($item->pbbkb_amount ?? 0) > 0)
                            <tr>
                                <td>{{ $itemNumber++ }}</td>
                                <td class="text-left">PBBKB</td>
                                <td class="text-right">
                                    ${{ number_format(($item->pbbkb_amount ?? 0) / ($item->quantity ?? 1), 0, ',', '.') }}
                                </td>
                                <td class="text-right">{{ number_format($item->quantity ?? 0, 2, ',', '.') }}
                                    {{ $item->unit ?? 'Liter' }}</td>
                                <td class="text-right"></td>
                                <td class="text-right">${{ number_format($item->pbbkb_amount ?? 0, 0, ',', '.') }}</td>
                            </tr>
                            @php
                                $totalSales += $item->pbbkb_amount ?? 0;
                            @endphp
                        @endif

                        {{-- VAT row --}}
                        @if (($item->ppn_amount ?? 0) > 0)
                            <tr>
                                <td>{{ $itemNumber++ }}</td>
                                <td class="text-left">VAT</td>
                                <td class="text-right"></td>
                                <td class="text-right"></td>
                                <td class="text-right"></td>
                                <td class="text-right">{{ $record->formatCurrency($item->ppn_amount ?? 0) }}</td>
                            </tr>
                            @php
                                $totalTax += $item->ppn_amount ?? 0;
                            @endphp
                        @endif
                    @endforeach
                @else
                    @if ($record->transaksiPenjualan && $record->transaksiPenjualan->penjualanDetails)
                        @foreach ($record->transaksiPenjualan->penjualanDetails as $detail)
                            @php
                                $total = $detail->volume_item * $detail->harga_jual;
                                $subtotalCalculated += $total;
                            @endphp
                            <tr>
                                <td>{{ $itemNumber++ }}</td>
                                <td style="text-align: left; padding-left: 10px;">
                                    {{ $detail->item->name ?? 'N/A' }}
                                </td>
                                <td>{{ number_format($detail->volume_item ?? 0, 0, ',', '.') }} L</td>
                                <td>${{ number_format($detail->harga_jual ?? 0, 0, ',', '.') }}</td>
                                <td>${{ number_format($total, 0, ',', '.') }}</td>
                            </tr>
                        @endforeach
                    @else
                        <tr>
                            <td colspan="6" style="text-align: center; color: #7f8c8d; padding: 20px;">
                                No items in this invoice
                            </td>
                        </tr>
                    @endif
                @endif

                <!-- Totals within table -->
                <tr class="totals-row">
                    <td colspan="5" style="text-align: right; font-weight: bold;">Total Sales</td>
                    <td class="text-right" style="font-weight: bold;">
                        ${{ number_format($totalSales, 0, ',', '.') }}
                    </td>
                </tr>

                <tr class="totals-row">
                    <td colspan="5" style="text-align: right; font-weight: bold;">Total Tax</td>
                    <td class="text-right" style="font-weight: bold;">${{ number_format($totalTax, 0, ',', '.') }}
                    </td>
                </tr>

                <tr class="final-total-row">
                    <td colspan="5" style="text-align: right; font-weight: bold;">Total Invoice Including Tax
                    </td>
                    <td class="text-right" style="font-weight: bold;">
                        ${{ number_format($totalSales + $totalTax, 0, ',', '.') }}</td>
                </tr>
            </tbody>
        </table>

        <!-- Notes Section -->
        <div class="terbilang-section">
            <strong>Notes :</strong>
            @php
                $totalInvoice = $totalSales + $totalTax;
                // Simple number to words conversion for English
                $terbilang =
                    $record->numberToWordsEnglish($totalInvoice) ?? 'Two Million Four Hundred Sixty Thousand rupiah';
            @endphp
            "{{ $terbilang }}"
        </div>

        <!-- Payment Notes -->
        <div class="payment-notes">
            1. Payment transfer to account<br>
            2. After Payment please Call or Email transfer from to 0761-22369 or <EMAIL>
        </div>

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-location">Pekanbaru,
                    {{ $record->tanggal_invoice ? $record->tanggal_invoice->format('d F Y') : now()->format('d F Y') }}
                </div>
                <div class="signature-space" style="height: 60px;"></div>
                <div class="signature-name"><strong>Agustiawan Syahputra</strong></div>
                <div class="signature-title">Managing Director</div>
            </div>
        </div>

        {{-- Footer Section --}}
        <div class="footer">
            <table>
                <tr>
                    <td style="width: 33%;" class="iso-logos">
                        @php
                            $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
                            $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
                                ->whereIn('name', $isoNamesToDisplay)
                                ->get();
                        @endphp

                        @foreach ($isoCertifications as $cert)
                            @php
                                $logoPath = public_path('storage/' . $cert->logo_path);
                            @endphp
                            @if (file_exists($logoPath))
                                <img src="data:image/jpeg;base64,{{ base64_encode(file_get_contents($logoPath)) }}"
                                    alt="{{ $cert->name }}" style="height: 40px; margin-right: 10px;">
                            @endif
                        @endforeach
                    </td>
                    <td style="width: 34%;" class="center">
                        <p><strong>PT. LINTAS RIAU PRIMA</strong></p>
                        @if ($record->letterSetting)
                            <p>{{ $record->letterSetting->address }}</p>
                            <p>{{ $record->letterSetting->city }}, {{ $record->letterSetting->province }}.
                                {{ $record->letterSetting->postal_code }}</p>
                        @else
                            <p>Jl. Mesjid Al Furqon No. 26</p>
                            <p>Pekanbaru, Riau. 28144</p>
                        @endif
                    </td>
                    <td style="width: 33%;" class="right">
                        @if ($record->letterSetting)
                            <p>Tel: {{ $record->letterSetting->phone_number }}</p>
                            <p>Email: {{ $record->letterSetting->email }}</p>
                            <p>Web: {{ $record->letterSetting->website }}</p>
                        @else
                            <p>Tel: 0761-22369</p>
                            <p>Email: <EMAIL></p>
                            <p>Web: www.lintasriauprima.com</p>
                        @endif
                    </td>
                </tr>
            </table>
        </div>
    </div>
</body>

</html>
