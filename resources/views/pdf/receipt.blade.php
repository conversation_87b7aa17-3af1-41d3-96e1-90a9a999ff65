<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kwitansi {{ $record->nomor_receipt ?? $record->nomor_invoice }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: 10px;
            line-height: 1.4;
            color: #333;
            padding: 20px;
        }

        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            border: 2px solid #333;
            padding: 30px;
            background: white;
            position: relative;
        }

        .header {
            /* overflow: hidden; Clear floats */
            margin-bottom: 40px;
        }

        .company-logo {
            float: left;
            width: 60px;
            height: 60px;
            display: inline-block;
            vertical-align: top;
        }

        .company-logo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .company-logo-text {
            background: linear-gradient(135deg, #3B82F6, #1E40AF);
            color: white;
            font-weight: bold;
            font-size: 10px;
            text-align: center;
            padding: 5px;
            border-radius: 8px;
            line-height: 1.2;
        }

        .receipt-title {
            display: inline-block;
            width: 60%;
            text-align: center;
            vertical-align: middle;
            padding: 0 10px;
        }

        .receipt-title h1 {
            font-size: 20px;
            font-weight: bold;
            color: #1f2937;
            text-decoration: underline;
            margin: 0;
            letter-spacing: 2px;
        }

        .receipt-number {
            float: center;
            display: inline-block;
            text-align: center;
            font-size: 10px;
            color: #374151;
            white-space: nowrap;
        }

        .receipt-body {
            margin-bottom: 40px;
        }

        /* Simplified inline-block styles */
        .receipt-row {
            margin-bottom: 20px;
            font-family: 'DejaVu Sans', Arial, sans-serif;
        }

        .receipt-label {
            display: inline-block;
            width: 180px;
            color: #374151;
            vertical-align: top;
        }

        .receipt-colon {
            display: inline-block;
            width: 20px;
            text-align: center;
            vertical-align: top;
        }

        .receipt-value {
            display: inline-block;
            border-bottom: 1px solid #333;
            padding-bottom: 5px;
            color: #1f2937;
            font-weight: 500;
            max-width: calc(100% - 200px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: top;
        }

        .amount-row .receipt-value {
            font-weight: bold;
            font-size: 12px;
        }

        .terbilang-row .receipt-value {
            font-style: italic;
            color: #374151;
            white-space: normal;
            /* Allow wrapping for longer text */
        }

        .payment-description {
            min-height: 60px;
            padding: 10px 0;
            white-space: normal;
            /* Allow wrapping for description */
        }

        .signature-section {
            margin-top: 60px;
            display: flex;
            justify-content: flex-end;
        }

        .signature-box {
            text-align: center;
            width: 250px;
        }

        .signature-location {
            margin-bottom: 5px;
            font-size: 12px;
        }

        .signature-space {
            height: 80px;
            margin: 20px 0;
            position: relative;
        }

        .signature-name {
            font-weight: bold;
            border-bottom: 1px solid #333;
            padding-bottom: 5px;
            margin-bottom: 5px;
        }

        .signature-title {
            font-size: 12px;
            color: #374151;
        }

        @media print {
            body {
                margin: 0;
                padding: 10px;
            }

            .receipt-container {
                border: 2px solid #333;
                box-shadow: none;
            }

            .receipt-row,
            .receipt-label,
            .receipt-colon,
            .receipt-value {
                font-family: 'DejaVu Sans', Arial, sans-serif !important;
                display: inline-block !important;
                vertical-align: top !important;
            }

            .header {
                overflow: auto;
            }

            .company-logo,
            .receipt-title,
            .receipt-number {
                display: inline-block !important;
                vertical-align: top !important;
            }
        }
    </style>
</head>

<body>
    <div class="receipt-container">
        <!-- Header Section -->
        <div class="header">
            <div class="company-logo">
                @if (isset($logoBase64) && $logoBase64)
                    <img src="data:image/png;base64,{{ $logoBase64 }}" alt="LRP Logo">
                @else
                    <div class="company-logo-text">
                        LINTAS<br>RIAU<br>PRIMA
                    </div>
                @endif
            </div>

            <div class="receipt-title">
                <h1>KWITANSI</h1>
                <p>
                    @if (isset($record->nomor_receipt))
                        No. Receipt : {{ $record->nomor_receipt }}
                    @else
                        No. Invoice : {{ $record->nomor_invoice }}
                    @endif
                </p>
            </div>

        </div>

        <!-- Receipt Body -->
        <div class="receipt-body">
            <div class="receipt-row">
                <span class="receipt-label">Sudah Terima Dari</span>
                <span class="receipt-colon">:</span>
                <span class="receipt-value">
                    @if (isset($record->nomor_receipt))
                        {{ $record->transaksiPenjualan->pelanggan->nama ?? ($record->invoice->transaksiPenjualan->pelanggan->nama ?? '-') }}
                    @else
                        {{ $record->transaksiPenjualan->pelanggan->nama ?? '-' }}
                    @endif
                </span>
            </div>

            <div class="receipt-row amount-row">
                <span class="receipt-label">Uang Sebanyak</span>
                <span class="receipt-colon">:</span>
                <span class="receipt-value">Rp
                    @if (isset($record->nomor_receipt))
                        {{ number_format($record->jumlah_pembayaran ?: $record->invoice->total_invoice ?? 0, 0, ',', '.') }}
                    @else
                        {{ number_format($record->total_invoice ?? 0, 0, ',', '.') }}
                    @endif
                </span>
            </div>

            <div class="receipt-row terbilang-row">
                <span class="receipt-label">Terbilang</span>
                <span class="receipt-colon">:</span>
                <span class="receipt-value">
                    @if (isset($record->nomor_receipt))
                        "{{ ucfirst(\App\Helpers\NumberToWords::convert($record->jumlah_pembayaran ?: $record->invoice->total_invoice ?? 0)) }}
                        rupiah"
                    @else
                        "{{ ucfirst(\App\Helpers\NumberToWords::convert($record->total_invoice ?? 0)) }} rupiah"
                    @endif
                </span>
            </div>

            <div class="receipt-row">
                <span class="receipt-label">Untuk Pembayaran</span>
                <span class="receipt-colon">:</span>
                <span class="receipt-value payment-description">
                    @php
                        // Get the transaction and determine the type
                        $transaksi = null;
                        $invoiceItems = collect();
                        $deliveryOrders = collect();

                        if (isset($record->nomor_receipt)) {
                            // This is a receipt
                            $transaksi = $record->transaksiPenjualan ?? $record->invoice?->transaksiPenjualan;
                            $invoiceItems = $record->invoice?->invoiceItems ?? collect();
                            $deliveryOrders = $transaksi?->deliveryOrders ?? collect();
                        } else {
                            // This is an invoice
                            $transaksi = $record->transaksiPenjualan;
                            $invoiceItems = $record->invoiceItems ?? collect();
                            $deliveryOrders = $transaksi?->deliveryOrders ?? collect();
                        }

                        $tipeTransaksi = $transaksi?->tipe ?? 'jasa';
                        $alamatTujuan =
                            $transaksi?->alamatPelanggan?->alamat ?? ($transaksi?->pelanggan?->alamat ?? '');

                        // Calculate total volume from delivery orders
                        $totalVolume = $deliveryOrders->sum('volume_do');

                        // Get invoice number for display
                        $invoiceNumber = isset($record->nomor_receipt)
                            ? $record->invoice?->nomor_invoice ?? $record->nomor_receipt
                            : $record->nomor_invoice;
                    @endphp

                    @if ($tipeTransaksi === 'dagang')
                        {{-- For trading transactions, show detailed BBM information --}}
                        Pembayaran Invoice {{ $invoiceNumber }} untuk pengiriman
                        @if ($totalVolume > 0)
                            {{ number_format($totalVolume, 0, ',', '.') }} liter BBM
                        @else
                            BBM
                        @endif
                        @if ($invoiceItems->isNotEmpty())
                            @foreach ($invoiceItems as $item)
                                @if (!$loop->first)
                                    ,
                                @endif
                                {{ $item->item_name ?? ($item->item_description ?? 'BBM') }}
                                @if ($item->quantity > 0)
                                    ({{ number_format($item->quantity, 0, ',', '.') }} {{ $item->unit ?? 'liter' }})
                                @endif
                            @endforeach
                        @endif
                        @if ($alamatTujuan)
                            ke {{ $alamatTujuan }}
                        @endif.
                    @else
                        {{-- For service transactions, show service description --}}
                        Pembayaran Invoice {{ $invoiceNumber }} untuk layanan
                        @if ($invoiceItems->isNotEmpty())
                            @foreach ($invoiceItems as $item)
                                @if (!$loop->first)
                                    ,
                                @endif
                                {{ $item->item_name ?? ($item->item_description ?? 'pengiriman BBM') }}
                            @endforeach
                        @else
                            pengiriman BBM
                        @endif
                        @if ($alamatTujuan)
                            ke {{ $alamatTujuan }}
                        @endif.
                    @endif
                </span>
            </div>
        </div>

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-location">Pekanbaru,
                    @if (isset($record->nomor_receipt))
                        {{ $record->tanggal_pembayaran ? $record->tanggal_pembayaran->format('d-M-y') : now()->format('d-M-y') }}
                    @else
                        {{ now()->format('d-M-y') }}
                    @endif
                </div>
                <div class="signature-space">
                    @if (isset($logoBase64) && $logoBase64)
                        <img src="data:image/png;base64,{{ $logoBase64 }}" alt="LRP Logo"
                            style="width: 60px; height: 60px; margin: 10px auto; display: block;">
                    @else
                        <div class="company-logo-text"
                            style="width: 60px; height: 60px; margin: 10px auto; font-size: 10px; padding: 5px;">
                            LINTAS<br>RIAU<br>PRIMA
                        </div>
                    @endif
                </div>
                <div class="signature-name">Agustiawan Syahputra</div>
                <div class="signature-title">Direktur</div>
            </div>
        </div>
    </div>
</body>

</html>
