# Combined Invoice & Receipt Templates

## Overview

Template gabungan ini menggabungkan invoice dan receipt dalam satu dokumen PDF, dimana bagian footer invoice tidak digunakan dan digantikan dengan receipt.

## File Structure

### Main Combined Template

-   `combined_receipt_invoice.blade.php` - Template utama yang menggabungkan invoice dan receipt

### Invoice Templates (No Footer)

-   `invoice_id_no_footer.blade.php` - Template invoice bahasa Indonesia tanpa footer
-   `invoice_en_no_footer.blade.php` - Template invoice bahasa Inggris tanpa footer

### Receipt Templates (Used as Footer)

-   `receipt_id.blade.php` - Template receipt bahasa Indonesia (format pakem dengan font diperkecil)
-   `receipt_en.blade.php` - Template receipt bahasa Inggris (format pakem dengan font diperkecil)

## How It Works

1. **Invoice Section**: Menampilkan invoice kompak dengan header minimal dan layout 2 kolom
2. **Receipt Section**: Menampilkan receipt format pakem dengan font diperkecil
3. **Single Page**: Kedua dokumen digabung dalam satu halaman A4
4. **Compact Layout**: Jarak minimal antara invoice dan receipt dengan garis pemisah tipis
5. **Space Optimization**: Company info dihapus, customer details dalam 2 kolom
6. **Complete Table**: Tabel invoice dengan kolom lengkap termasuk PBBKB

## CSS Classes Used

### Layout Classes

-   `.combined-document` - Container utama untuk dokumen gabungan
-   `.invoice-section` - Section untuk invoice (tanpa footer)
-   `.receipt-section` - Section untuk receipt (sebagai footer)

### Hidden Elements

-   `.invoice-section .footer-section` - Menyembunyikan footer invoice
-   `.invoice-section .signature-section` - Menyembunyikan signature invoice

## Template Selection Logic

```php
@php
    $invoiceTemplate = $locale === 'en' ? 'pdf.invoice_en_no_footer' : 'pdf.invoice_id_no_footer';
    $fallbackTemplate = $locale === 'en' ? 'pdf.invoice_en' : 'pdf.invoice_id';
@endphp

@if (View::exists($invoiceTemplate))
    @include($invoiceTemplate, ['record' => $record, 'logoBase64' => $logoBase64])
@else
    @include($fallbackTemplate, ['record' => $record, 'logoBase64' => $logoBase64])
@endif
```

## Supported Locales

-   `id` - Bahasa Indonesia
-   `en` - English

## Usage in Controller

```php
// Generate combined PDF
$combinedPdf = Pdf::loadView('pdf.combined_receipt_invoice', [
    'record' => $invoice,
    'logoBase64' => $logoBase64,
    'locale' => $locale
])->setPaper('a4', 'portrait');
```

## Benefits

1. **Space Efficient**: Menggunakan ruang footer invoice yang biasanya kosong
2. **Single Document**: Satu PDF berisi invoice dan receipt
3. **Professional Layout**: Tetap terlihat rapi dan profesional
4. **Multi-language**: Support bahasa Indonesia dan Inggris
5. **Fallback Support**: Menggunakan template standar jika template no_footer tidak ada

## Customization

Untuk mengubah layout atau styling:

1. Edit CSS di `combined_receipt_invoice.blade.php`
2. Modifikasi template `invoice_*_no_footer.blade.php` untuk bagian invoice
3. Modifikasi template `receipt_*.blade.php` untuk bagian receipt

## Print Options Available

1. **Print Receipt** - Receipt saja
2. **Print Invoice** - Invoice saja
3. **Print Receipt & Invoice** - Gabungan keduanya (menggunakan template ini)
