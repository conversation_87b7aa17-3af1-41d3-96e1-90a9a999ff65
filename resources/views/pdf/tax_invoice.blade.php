<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tax Invoice - {{ $record->nomor_tax_invoice }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }

        .tax-invoice-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }

        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }

        .company-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            border: 2px solid #2563eb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
        }

        .company-logo img {
            max-width: 70px;
            max-height: 70px;
            object-fit: contain;
        }

        .company-logo-fallback {
            font-size: 10px;
            font-weight: bold;
            text-align: center;
            color: #2563eb;
            line-height: 1.2;
        }

        .company-info {
            flex: 1;
        }

        .company-name {
            font-size: 18px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 5px;
        }

        .company-tagline {
            font-size: 11px;
            color: #64748b;
            font-style: italic;
            margin-bottom: 3px;
        }

        .company-services {
            font-size: 10px;
            color: #64748b;
        }

        .document-title {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 30px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Tax Invoice Info */
        .tax-invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .info-section {
            width: 48%;
        }

        .info-section h3 {
            font-size: 14px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 10px;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 5px;
        }

        .info-row {
            display: flex;
            margin-bottom: 5px;
        }

        .info-label {
            width: 120px;
            font-weight: 600;
            color: #374151;
        }

        .info-value {
            flex: 1;
            color: #111827;
        }

        /* Tax Details Table */
        .tax-details {
            margin-bottom: 30px;
        }

        .tax-details h3 {
            font-size: 14px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 15px;
        }

        .tax-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #d1d5db;
        }

        .tax-table th,
        .tax-table td {
            padding: 12px;
            text-align: left;
            border: 1px solid #d1d5db;
        }

        .tax-table th {
            background-color: #f3f4f6;
            font-weight: bold;
            color: #374151;
        }

        .tax-table .text-right {
            text-align: right;
        }

        .tax-table .text-center {
            text-align: center;
        }

        /* Summary Section */
        .summary-section {
            margin-top: 30px;
            display: flex;
            justify-content: flex-end;
        }

        .summary-table {
            width: 300px;
            border-collapse: collapse;
        }

        .summary-table td {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
        }

        .summary-table .label {
            background-color: #f9fafb;
            font-weight: 600;
            width: 60%;
        }

        .summary-table .value {
            text-align: right;
            font-weight: 600;
        }

        .summary-table .total-row {
            background-color: #1e40af;
            color: white;
            font-weight: bold;
        }

        /* Footer */
        .footer {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .notes-section {
            width: 60%;
        }

        .notes-section h4 {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #374151;
        }

        .notes-section p {
            font-size: 11px;
            color: #6b7280;
            line-height: 1.5;
        }

        .signature-section {
            width: 35%;
            text-align: center;
        }

        .signature-section p {
            margin-bottom: 5px;
            font-size: 11px;
        }

        .signature-box {
            height: 80px;
            border: 1px solid #d1d5db;
            margin: 15px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f9fafb;
        }

        .signature-name {
            font-weight: bold;
            text-decoration: underline;
        }

        /* Status Badge */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-approved {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-rejected {
            background-color: #fee2e2;
            color: #991b1b;
        }

        /* Utility Classes */
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .font-bold { font-weight: bold; }
        .mb-10 { margin-bottom: 10px; }
        .mb-20 { margin-bottom: 20px; }

        /* Print Styles */
        @media print {
            body { margin: 0; }
            .tax-invoice-container { 
                max-width: none; 
                margin: 0; 
                padding: 15px; 
            }
        }
    </style>
</head>

<body>
    <div class="tax-invoice-container">
        <!-- Header Section -->
        <div class="header">
            <div class="company-section">
                <div class="company-logo {{ empty($logoBase64) ? 'company-logo-fallback' : '' }}">
                    @if (!empty($logoBase64))
                        <img src="data:image/png;base64,{{ $logoBase64 }}" alt="Company Logo">
                    @else
                        LINTAS<br>RIAU<br>PRIMA
                    @endif
                </div>
                <div class="company-info">
                    <div class="company-name">PT. LINTAS RIAU PRIMA</div>
                    <div class="company-tagline">TRUSTED & RELIABLE PARTNER</div>
                    <div class="company-services">Fuel Agent - Fuel Transportation - Bunker Service</div>
                </div>
            </div>
        </div>

        <!-- Document Title -->
        <div class="document-title">
            FAKTUR PAJAK
        </div>

        <!-- Tax Invoice Information -->
        <div class="tax-invoice-info">
            <div class="info-section">
                <h3>Informasi Faktur Pajak</h3>
                <div class="info-row">
                    <span class="info-label">Nomor:</span>
                    <span class="info-value">{{ $record->nomor_tax_invoice }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Tanggal:</span>
                    <span class="info-value">{{ $record->tanggal_tax_invoice->format('d F Y') }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Status:</span>
                    <span class="info-value">
                        <span class="status-badge status-{{ $record->status }}">
                            {{ ucfirst($record->status) }}
                        </span>
                    </span>
                </div>
                @if($record->invoice)
                <div class="info-row">
                    <span class="info-label">Invoice:</span>
                    <span class="info-value">{{ $record->invoice->nomor_invoice }}</span>
                </div>
                @endif
            </div>

            <div class="info-section">
                <h3>Informasi Perusahaan</h3>
                <div class="info-row">
                    <span class="info-label">Nama:</span>
                    <span class="info-value">{{ $record->nama_perusahaan }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Alamat:</span>
                    <span class="info-value">{{ $record->alamat_perusahaan }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">NPWP:</span>
                    <span class="info-value">{{ $record->npwp_perusahaan }}</span>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="tax-invoice-info">
            <div class="info-section">
                <h3>Informasi Pelanggan</h3>
                <div class="info-row">
                    <span class="info-label">Nama:</span>
                    <span class="info-value">{{ $record->nama_pelanggan }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Alamat:</span>
                    <span class="info-value">{{ $record->alamat_pelanggan }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">NPWP:</span>
                    <span class="info-value">{{ $record->npwp_pelanggan ?: '-' }}</span>
                </div>
            </div>
        </div>

        <!-- Tax Details -->
        <div class="tax-details">
            <h3>Rincian Pajak</h3>
            <table class="tax-table">
                <thead>
                    <tr>
                        <th class="text-center" style="width: 5%">No</th>
                        <th>Keterangan</th>
                        <th class="text-right" style="width: 20%">Jumlah</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-center">1</td>
                        <td>Dasar Pengenaan Pajak (DPP)</td>
                        <td class="text-right">Rp {{ number_format($record->dasar_pengenaan_pajak, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td class="text-center">2</td>
                        <td>Tarif Pajak ({{ $record->tarif_pajak }}%)</td>
                        <td class="text-right">{{ $record->tarif_pajak }}%</td>
                    </tr>
                    <tr>
                        <td class="text-center">3</td>
                        <td>Pajak Pertambahan Nilai (PPN)</td>
                        <td class="text-right">Rp {{ number_format($record->pajak_pertambahan_nilai, 0, ',', '.') }}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Summary Section -->
        <div class="summary-section">
            <table class="summary-table">
                <tr>
                    <td class="label">Total Tax Invoice:</td>
                    <td class="value">Rp {{ number_format($record->total_tax_invoice, 0, ',', '.') }}</td>
                </tr>
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="notes-section">
                @if($record->catatan)
                <h4>Catatan:</h4>
                <p>{{ $record->catatan }}</p>
                @endif
            </div>

            <div class="signature-section">
                <p>Pekanbaru, {{ $record->tanggal_tax_invoice->format('d F Y') }}</p>
                <p>Hormat kami,</p>
                <div class="signature-box">
                    <!-- Signature placeholder -->
                </div>
                <p class="signature-name">{{ $record->createdBy?->name ?? 'Admin' }}</p>
                <p>{{ $record->createdBy?->jabatan?->nama ?? 'Manager Keuangan' }}</p>
            </div>
        </div>
    </div>
</body>
</html>
