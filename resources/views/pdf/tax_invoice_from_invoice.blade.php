<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faktur <PERSON> - {{ $record->nomor_invoice }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }

        .tax-invoice-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }

        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }

        .company-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9fafb;
        }

        .company-logo img {
            max-width: 70px;
            max-height: 70px;
            object-fit: contain;
        }

        .company-logo-fallback {
            font-size: 10px;
            font-weight: bold;
            text-align: center;
            color: #6b7280;
            line-height: 1.2;
        }

        .company-info {
            flex: 1;
        }

        .company-name {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .company-tagline {
            font-size: 12px;
            color: #2563eb;
            font-weight: 600;
            margin-bottom: 3px;
        }

        .company-services {
            font-size: 10px;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .company-contact {
            font-size: 10px;
            color: #374151;
            line-height: 1.3;
        }

        /* Document Title */
        .document-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 30px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        /* Invoice Info Section */
        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 40px;
        }

        .invoice-details, .customer-details {
            flex: 1;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }

        .detail-row {
            display: flex;
            margin-bottom: 5px;
        }

        .detail-label {
            width: 120px;
            font-weight: 600;
            color: #374151;
        }

        .detail-value {
            flex: 1;
            color: #1f2937;
        }

        /* Items Table */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border: 1px solid #d1d5db;
        }

        .items-table th {
            background: #f3f4f6;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            color: #1f2937;
            border: 1px solid #d1d5db;
            font-size: 11px;
        }

        .items-table td {
            padding: 10px 8px;
            border: 1px solid #d1d5db;
            font-size: 11px;
            vertical-align: top;
        }

        .items-table tr:nth-child(even) {
            background: #f9fafb;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        /* Tax Calculation Section */
        .tax-calculation {
            margin-top: 30px;
            border: 2px solid #2563eb;
            padding: 20px;
            background: #f8fafc;
        }

        .tax-title {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 15px;
            text-align: center;
        }

        .tax-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }

        .tax-row.total {
            border-top: 2px solid #2563eb;
            margin-top: 10px;
            padding-top: 10px;
            font-weight: bold;
            font-size: 14px;
        }

        .tax-label {
            font-weight: 600;
            color: #374151;
        }

        .tax-value {
            font-weight: bold;
            color: #1f2937;
        }

        /* Footer */
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }

        /* Print Styles */
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .tax-invoice-container {
                margin: 0;
                padding: 15px;
                max-width: none;
            }
        }
    </style>
</head>

<body>
    <div class="tax-invoice-container">
        <!-- Header Section -->
        <div class="header">
            <div class="company-section">
                <div class="company-logo {{ empty($logoBase64) ? 'company-logo-fallback' : '' }}">
                    @if (!empty($logoBase64))
                        <img src="data:image/png;base64,{{ $logoBase64 }}" alt="Company Logo">
                    @else
                        LINTAS<br>RIAU<br>PRIMA
                    @endif
                </div>
                <div class="company-info">
                    <div class="company-name">PT. LINTAS RIAU PRIMA</div>
                    <div class="company-tagline">TRUSTED & RELIABLE PARTNER</div>
                    <div class="company-services">Fuel Agent - Fuel Transportation - Bunker Service</div>
                    <div class="company-contact">
                        Jl. Raya Industri No. 123, Pekanbaru, Riau 28284<br>
                        Telp: (0761) 123-4567 | Email: <EMAIL><br>
                        NPWP: 01.234.567.8-901.000
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Title -->
        <div class="document-title">
            FAKTUR PAJAK
        </div>

        <!-- Invoice Info Section -->
        <div class="invoice-info">
            <div class="invoice-details">
                <div class="section-title">Informasi Faktur Pajak</div>
                <div class="detail-row">
                    <div class="detail-label">Nomor Faktur:</div>
                    <div class="detail-value">{{ $record->nomor_invoice }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Tanggal:</div>
                    <div class="detail-value">{{ $record->tanggal_invoice ? $record->tanggal_invoice->format('d F Y') : '-' }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Jatuh Tempo:</div>
                    <div class="detail-value">{{ $record->tanggal_jatuh_tempo ? $record->tanggal_jatuh_tempo->format('d F Y') : '-' }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Status:</div>
                    <div class="detail-value">{{ ucfirst($record->status) }}</div>
                </div>
            </div>

            <div class="customer-details">
                <div class="section-title">Informasi Pelanggan</div>
                <div class="detail-row">
                    <div class="detail-label">Nama:</div>
                    <div class="detail-value">{{ $record->nama_pelanggan ?: ($record->transaksiPenjualan->pelanggan->nama ?? 'N/A') }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Alamat:</div>
                    <div class="detail-value">{{ $record->alamat_pelanggan ?: ($record->transaksiPenjualan->pelanggan->alamatUtama->alamat ?? 'N/A') }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">NPWP:</div>
                    <div class="detail-value">{{ $record->npwp_pelanggan ?: ($record->transaksiPenjualan->pelanggan->npwp ?? 'N/A') }}</div>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">No</th>
                    <th style="width: 40%;">Deskripsi Barang/Jasa</th>
                    <th style="width: 10%;">Qty</th>
                    <th style="width: 10%;">Satuan</th>
                    <th style="width: 15%;">Harga Satuan</th>
                    <th style="width: 20%;">Jumlah</th>
                </tr>
            </thead>
            <tbody>
                @if($record->invoiceItems && $record->invoiceItems->count() > 0)
                    @foreach($record->invoiceItems as $index => $item)
                    <tr>
                        <td class="text-center">{{ $index + 1 }}</td>
                        <td>{{ $item->item->name ?? 'N/A' }}</td>
                        <td class="text-right">{{ number_format($item->quantity, 2) }}</td>
                        <td class="text-center">{{ $item->item->satuan->nama ?? 'Unit' }}</td>
                        <td class="text-right">Rp {{ number_format($item->unit_price, 2, ',', '.') }}</td>
                        <td class="text-right">Rp {{ number_format($item->total_price, 2, ',', '.') }}</td>
                    </tr>
                    @endforeach
                @elseif($record->transaksiPenjualan && $record->transaksiPenjualan->penjualanDetails)
                    @foreach($record->transaksiPenjualan->penjualanDetails as $index => $detail)
                    <tr>
                        <td class="text-center">{{ $index + 1 }}</td>
                        <td>{{ $detail->item->name ?? 'N/A' }}</td>
                        <td class="text-right">{{ number_format($detail->quantity, 2) }}</td>
                        <td class="text-center">{{ $detail->item->satuanDasar->nama ?? 'Unit' }}</td>
                        <td class="text-right">Rp {{ number_format($detail->unit_price, 2, ',', '.') }}</td>
                        <td class="text-right">Rp {{ number_format($detail->total_price, 2, ',', '.') }}</td>
                    </tr>
                    @endforeach
                @else
                    <tr>
                        <td colspan="6" class="text-center">Tidak ada item</td>
                    </tr>
                @endif
            </tbody>
        </table>

        <!-- Tax Calculation Section -->
        <div class="tax-calculation">
            <div class="tax-title">PERHITUNGAN PAJAK</div>
            
            <div class="tax-row">
                <div class="tax-label">Dasar Pengenaan Pajak (DPP):</div>
                <div class="tax-value">Rp {{ number_format($record->subtotal, 2, ',', '.') }}</div>
            </div>
            
            <div class="tax-row">
                <div class="tax-label">PPN (11%):</div>
                <div class="tax-value">Rp {{ number_format($record->total_pajak, 2, ',', '.') }}</div>
            </div>
            
            @if($record->biaya_ongkos_angkut > 0)
            <div class="tax-row">
                <div class="tax-label">Biaya Ongkos Angkut:</div>
                <div class="tax-value">Rp {{ number_format($record->biaya_ongkos_angkut, 2, ',', '.') }}</div>
            </div>
            @endif
            
            @if($record->biaya_pbbkb > 0)
            <div class="tax-row">
                <div class="tax-label">Biaya PBBKB:</div>
                <div class="tax-value">Rp {{ number_format($record->biaya_pbbkb, 2, ',', '.') }}</div>
            </div>
            @endif
            
            @if($record->biaya_operasional_kerja > 0)
            <div class="tax-row">
                <div class="tax-label">Biaya Operasional Kerja:</div>
                <div class="tax-value">Rp {{ number_format($record->biaya_operasional_kerja, 2, ',', '.') }}</div>
            </div>
            @endif
            
            <div class="tax-row total">
                <div class="tax-label">TOTAL FAKTUR PAJAK:</div>
                <div class="tax-value">Rp {{ number_format($record->total_invoice, 2, ',', '.') }}</div>
            </div>
        </div>

        @if($record->catatan)
        <div style="margin-top: 20px;">
            <div class="section-title">Catatan</div>
            <p style="font-size: 11px; color: #374151;">{{ $record->catatan }}</p>
        </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <p>Dokumen ini dibuat secara elektronik dan sah tanpa tanda tangan basah.</p>
            <p>PT. LINTAS RIAU PRIMA - {{ now()->format('d F Y H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
