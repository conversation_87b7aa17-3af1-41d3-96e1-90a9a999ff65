<!DOCTYPE html>
<html lang="{{ $locale ?? 'id' }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice & Receipt - {{ $record->nomor_invoice }}</title>
    <style>
        @page {
            size: A4;
            margin: 0.3in;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
        }

        .combined-document {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .invoice-section {
            width: 100%;
            flex: 1;
            margin-bottom: 0px;
            padding-bottom: 3px;
            max-height: 60%;
            overflow: hidden;
        }

        .receipt-section {
            width: 100%;
            flex: 0 0 auto;
            margin-top: 0px;
            padding-top: 3px;
            border-top: 1px dashed #666;
            max-height: 40%;
            font-size: 9px;
        }

        /* Hide invoice footer but keep signature */
        .invoice-section .footer-section {
            display: none;
        }

        /* Compact invoice elements */
        .invoice-section .total-section {
            margin-bottom: 0 !important;
            margin-top: 8px !important;
        }

        .invoice-section .items-table {
            margin-bottom: 5px !important;
            font-size: 9px !important;
        }

        .invoice-section .header {
            margin-bottom: 10px !important;
        }

        .invoice-section .invoice-details {
            margin-bottom: 10px !important;
        }

        /* Compact receipt elements */
        .receipt-section {
            page-break-inside: avoid;
            margin-top: 0 !important;
        }

        .receipt-section * {
            margin-top: 0 !important;
            margin-bottom: 3px !important;
            line-height: 1.1 !important;
        }

        .receipt-section .header {
            margin-bottom: 5px !important;
            font-size: 8px !important;
        }

        .receipt-section table {
            font-size: 8px !important;
            margin-bottom: 3px !important;
        }

        .receipt-section .signature-section {
            margin-top: 5px !important;
            font-size: 7px !important;
        }

        /* Prevent page breaks */
        .combined-document,
        .invoice-section,
        .receipt-section {
            page-break-inside: avoid !important;
            break-inside: avoid !important;
        }
    </style>
</head>

<body>
    <div class="combined-document">
        <!-- Invoice Section (without footer) -->
        <div class="invoice-section">
            @php
                $invoiceTemplate = $locale === 'en' ? 'pdf.invoice_en_no_footer' : 'pdf.invoice_id_no_footer';
                $fallbackTemplate = $locale === 'en' ? 'pdf.invoice_en' : 'pdf.invoice_id';
            @endphp

            @if (View::exists($invoiceTemplate))
                @include($invoiceTemplate, ['record' => $record, 'logoBase64' => $logoBase64])
            @else
                @include($fallbackTemplate, ['record' => $record, 'logoBase64' => $logoBase64])
            @endif
        </div>

        <!-- Receipt Section (using original template with smaller font) -->
        <div class="receipt-section">
            @include("pdf.receipt_{$locale}", ['record' => $record, 'logoBase64' => $logoBase64])
        </div>
    </div>
</body>

</html>
