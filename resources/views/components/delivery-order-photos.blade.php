@if($getRecord()->pengirimanDriver && $getRecord()->pengirimanDriver->getMedia()->count() > 0)
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @php
            $pengirimanDriver = $getRecord()->pengirimanDriver;
            $photoCollections = [
                'foto_totalizer_awal' => 'Foto Totalizer Awal',
                'foto_totalizer_akhir' => 'Foto Totalizer Akhir',
                'foto_pemutusan_segel_atas' => 'Foto Pemutusan Segel Atas',
                'foto_pemutusan_segel_bawah' => 'Foto Pemutusan Segel Bawah',
                'foto_tera' => 'Foto Tera',
                'foto_sample_bbm' => 'Foto Sample BBM',
                'foto_tangki_mt_kosong' => 'Foto Tangki MT Kosong',
                'foto_mt' => 'Foto MT',
                'foto_pembongkaran' => 'Foto Pembongkaran',
            ];
        @endphp

        @foreach($photoCollections as $collection => $label)
            @php
                $media = $pengirimanDriver->getMedia($collection);
            @endphp
            
            @if($media->count() > 0)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                        <h4 class="text-sm font-semibold text-gray-900">{{ $label }}</h4>
                    </div>
                    <div class="p-4">
                        @foreach($media as $mediaItem)
                            <div class="mb-4 last:mb-0">
                                <img 
                                    src="{{ $mediaItem->getUrl() }}" 
                                    alt="{{ $label }}"
                                    class="w-full h-48 object-cover rounded-lg shadow-sm cursor-pointer hover:shadow-md transition-shadow duration-200"
                                    onclick="openImageModal('{{ $mediaItem->getUrl() }}', '{{ $label }}')"
                                >
                                <div class="mt-2 text-xs text-gray-500">
                                    <div>Ukuran: {{ $mediaItem->human_readable_size }}</div>
                                    <div>Diupload: {{ $mediaItem->created_at->format('d M Y H:i') }}</div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        @endforeach
    </div>

    <!-- Modal untuk preview gambar -->
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden items-center justify-center p-4">
        <div class="relative max-w-4xl max-h-full">
            <button 
                onclick="closeImageModal()" 
                class="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
            >
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <img 
                id="modalImage" 
                src="" 
                alt="" 
                class="max-w-full max-h-full object-contain rounded-lg"
            >
            <div id="modalTitle" class="absolute bottom-4 left-4 text-white bg-black bg-opacity-50 px-3 py-1 rounded"></div>
        </div>
    </div>

    <script>
        function openImageModal(imageUrl, title) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('modalTitle');
            
            modalImage.src = imageUrl;
            modalImage.alt = title;
            modalTitle.textContent = title;
            
            modal.classList.remove('hidden');
            modal.classList.add('flex');
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
            
            // Restore body scroll
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside the image
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>

    <style>
        /* Ensure modal appears above Filament's z-index layers */
        #imageModal {
            z-index: 9999 !important;
        }
    </style>
@else
    <div class="text-center py-8 text-gray-500">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        <p class="mt-2">Tidak ada foto pengiriman yang tersedia</p>
    </div>
@endif
