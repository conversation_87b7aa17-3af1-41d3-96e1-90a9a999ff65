@php
    $sealsWithPhotos = $getRecord()->seals->filter(fn($seal) => $seal->getFirstMedia('foto_segel'));
@endphp

@if($sealsWithPhotos->count() > 0)
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach($sealsWithPhotos as $seal)
            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
                {{-- Header dengan info segel --}}
                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-semibold text-gray-900 flex items-center">
                                <svg class="w-4 h-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                {{ $seal->nomor_segel }}
                            </h4>
                            <p class="text-xs text-gray-500 mt-1">
                                @switch($seal->jenis_segel)
                                    @case('atas')
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                            Segel Atas
                                        </span>
                                        @break
                                    @case('bawah')
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                            Segel Bawah
                                        </span>
                                        @break
                                    @case('samping')
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Segel Samping
                                        </span>
                                        @break
                                    @case('lainnya')
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            Lainnya
                                        </span>
                                        @break
                                    @default
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            {{ $seal->jenis_segel }}
                                        </span>
                                @endswitch
                            </p>
                        </div>
                        @if($seal->urutan)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                #{{ $seal->urutan }}
                            </span>
                        @endif
                    </div>
                </div>

                {{-- Foto segel --}}
                <div class="relative">
                    @php
                        $media = $seal->getFirstMedia('foto_segel');
                        $imageUrl = $media ? $media->getUrl('preview') : null;
                        $originalUrl = $media ? $media->getUrl() : null;
                    @endphp
                    
                    @if($imageUrl)
                        <div class="aspect-square bg-gray-100">
                            <img 
                                src="{{ $imageUrl }}" 
                                alt="Foto Segel {{ $seal->nomor_segel }}"
                                class="w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity"
                                onclick="openImageModal('{{ $originalUrl }}', '{{ $seal->nomor_segel }}')"
                            >
                        </div>
                        
                        {{-- Overlay dengan info tambahan --}}
                        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3">
                            <div class="text-white text-xs">
                                <p class="font-medium">{{ $media->name }}</p>
                                <p class="opacity-75">{{ $media->human_readable_size }}</p>
                            </div>
                        </div>
                    @endif
                </div>

                {{-- Keterangan jika ada --}}
                @if($seal->keterangan)
                    <div class="px-4 py-3 bg-gray-50 border-t border-gray-200">
                        <p class="text-sm text-gray-600">
                            <span class="font-medium">Keterangan:</span>
                            {{ $seal->keterangan }}
                        </p>
                    </div>
                @endif
            </div>
        @endforeach
    </div>

    {{-- Modal untuk preview gambar --}}
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center p-4">
        <div class="relative max-w-4xl max-h-full">
            <button 
                onclick="closeImageModal()" 
                class="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
            >
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <img id="modalImage" src="" alt="" class="max-w-full max-h-full object-contain">
            <div id="modalCaption" class="absolute bottom-4 left-4 right-4 text-white text-center bg-black bg-opacity-50 rounded px-4 py-2"></div>
        </div>
    </div>

    <script>
        function openImageModal(imageUrl, sealNumber) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalCaption = document.getElementById('modalCaption');
            
            modalImage.src = imageUrl;
            modalImage.alt = 'Foto Segel ' + sealNumber;
            modalCaption.textContent = 'Foto Segel ' + sealNumber;
            
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside the image
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>
@else
    <div class="text-center py-8 text-gray-500">
        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
        </svg>
        <p class="text-sm">Belum ada foto segel yang diunggah</p>
    </div>
@endif
