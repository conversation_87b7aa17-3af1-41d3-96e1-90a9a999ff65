<div class="space-y-4">
    @if($media)
        <div class="bg-white rounded-lg border border-gray-200 p-4">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        @if(strtolower($media->getExtensionAttribute()) === 'pdf')
                            <svg class="w-8 h-8 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                            </svg>
                        @else
                            <svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                            </svg>
                        @endif
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">{{ $media->name }}</h3>
                        <p class="text-xs text-gray-500">
                            {{ strtoupper($media->getExtensionAttribute()) }} • 
                            {{ $media->human_readable_size }}
                        </p>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <a href="{{ $media->getUrl() }}" 
                       target="_blank"
                       class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        Preview
                    </a>
                    <a href="{{ $media->getUrl() }}" 
                       download="{{ $media->name }}"
                       class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download
                    </a>
                </div>
            </div>
            
            @if(strtolower($media->getExtensionAttribute()) === 'pdf')
                <!-- PDF Preview -->
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <iframe 
                        src="{{ $media->getUrl() }}#toolbar=1&navpanes=0&scrollbar=1" 
                        class="w-full h-96"
                        frameborder="0">
                        <p>Your browser does not support PDFs. 
                           <a href="{{ $media->getUrl() }}" target="_blank">Download the PDF</a>.
                        </p>
                    </iframe>
                </div>
            @elseif(in_array(strtolower($media->getExtensionAttribute()), ['jpg', 'jpeg', 'png', 'gif']))
                <!-- Image Preview -->
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <img src="{{ $media->getUrl() }}" 
                         alt="{{ $media->name }}" 
                         class="w-full h-auto max-h-96 object-contain">
                </div>
            @else
                <!-- Other File Types -->
                <div class="border border-gray-200 rounded-lg p-8 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Preview not available</h3>
                    <p class="mt-1 text-sm text-gray-500">This file type cannot be previewed in the browser.</p>
                    <div class="mt-4">
                        <a href="{{ $media->getUrl() }}" 
                           target="_blank"
                           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            Open File
                        </a>
                    </div>
                </div>
            @endif
        </div>
    @else
        <div class="border border-gray-200 rounded-lg p-8 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No document available</h3>
            <p class="mt-1 text-sm text-gray-500">No {{ $documentType ?? 'document' }} has been uploaded yet.</p>
        </div>
    @endif
</div>
