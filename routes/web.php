<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect('/admin');
});

// Geofencing validation route for karyawan panel
Route::post('/karyawan/validate-geofencing', [App\Http\Controllers\GeofencingController::class, 'validateAttendanceLocation'])
    ->middleware(['auth'])
    ->name('karyawan.validate-geofencing');

// UangJalan receipt routes
Route::get('/uang-jalan/{uangJalan}/print-receipt', [App\Http\Controllers\UangJalanReceiptController::class, 'printReceipt'])
    ->middleware(['auth'])
    ->name('uang-jalan.print-receipt');

Route::get('/uang-jalan/{uangJalan}/preview-receipt', [App\Http\Controllers\UangJalanReceiptController::class, 'previewReceipt'])
    ->middleware(['auth'])
    ->name('uang-jalan.preview-receipt');
