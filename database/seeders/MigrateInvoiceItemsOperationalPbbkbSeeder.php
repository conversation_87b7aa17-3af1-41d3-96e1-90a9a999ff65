<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Invoice;
use App\Models\InvoiceItem;

class MigrateInvoiceItemsOperationalPbbkbSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $this->command->info('Migrating operational and PBBKB data to invoice items...');

        $invoices = Invoice::with('invoiceItems')->get();

        $updated = 0;
        foreach ($invoices as $invoice) {
            if ($invoice->invoiceItems->count() > 0) {
                $totalVolume = $invoice->invoiceItems->sum('quantity');
                
                // Distribute operational cost per item if invoice has operational
                if ($invoice->include_operasional_kerja && $invoice->biaya_operasional_kerja > 0 && $totalVolume > 0) {
                    $operationalRatePerLiter = $invoice->biaya_operasional_kerja / $totalVolume;
                    
                    foreach ($invoice->invoiceItems as $item) {
                        $item->include_operasional = true;
                        $item->operasional_rate = $operationalRatePerLiter;
                        $item->operasional_amount = $item->quantity * $operationalRatePerLiter;
                        $item->save();
                    }
                }
                
                // Distribute PBBKB cost per item if invoice has PBBKB
                if ($invoice->include_pbbkb && $invoice->biaya_pbbkb > 0 && $totalVolume > 0) {
                    $pbbkbRatePerLiter = $invoice->biaya_pbbkb / $totalVolume;
                    
                    foreach ($invoice->invoiceItems as $item) {
                        $item->include_pbbkb = true;
                        $item->pbbkb_rate = $pbbkbRatePerLiter;
                        $item->pbbkb_amount = $item->quantity * $pbbkbRatePerLiter;
                        $item->save();
                    }
                }

                $updated++;
                $this->command->info("Updated invoice {$invoice->nomor_invoice} with {$invoice->invoiceItems->count()} items");
            }
        }

        $this->command->info("Successfully updated {$updated} invoices with operational and PBBKB data per item.");
    }
}
