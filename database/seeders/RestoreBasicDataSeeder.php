<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Jabatan;
use App\Models\Divisi;
use App\Models\Entitas;
use App\Models\PaymentMethod;

class RestoreBasicDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create basic organizational structure
        $jabatan = Jabatan::firstOrCreate(['nama_jabatan' => 'Administrator']);
        $divisi = Divisi::firstOrCreate(['nama_divisi' => 'IT']);
        
        // Get or create entitas
        $entitas = Entitas::first();
        if (!$entitas) {
            $entitas = Entitas::create([
                'nama_entitas' => 'PT. Lintas Riau Prima',
                'alamat' => 'Pekanbaru, Riau',
                'telepon' => '0761-123456',
                'email' => '<EMAIL>',
            ]);
        }

        // Create admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrator',
                'password' => bcrypt('password'),
                'role' => 'superadmin',
                'id_jabatan' => $jabatan->id,
                'id_divisi' => $divisi->id,
                'id_entitas' => $entitas->id,
                'is_active' => true,
                'no_induk' => 'ADM001',
                'hp' => '08123456789'
            ]
        );

        // Create some sample users
        $salesJabatan = Jabatan::firstOrCreate(['nama_jabatan' => 'Sales']);
        $salesDivisi = Divisi::firstOrCreate(['nama_divisi' => 'Sales & Marketing']);

        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sales Manager',
                'password' => bcrypt('password'),
                'role' => 'admin',
                'id_jabatan' => $salesJabatan->id,
                'id_divisi' => $salesDivisi->id,
                'id_entitas' => $entitas->id,
                'is_active' => true,
                'no_induk' => 'SLS001',
                'hp' => '08123456790'
            ]
        );

        $driverJabatan = Jabatan::firstOrCreate(['nama_jabatan' => 'Driver']);
        $operasionalDivisi = Divisi::firstOrCreate(['nama_divisi' => 'Operasional']);

        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Driver',
                'password' => bcrypt('password'),
                'role' => 'driver',
                'id_jabatan' => $driverJabatan->id,
                'id_divisi' => $operasionalDivisi->id,
                'id_entitas' => $entitas->id,
                'is_active' => true,
                'no_induk' => 'DRV001',
                'hp' => '08123456791'
            ]
        );

        echo "✅ Basic users created successfully!\n";
        echo "Admin: <EMAIL> / password\n";
        echo "Sales: <EMAIL> / password\n";
        echo "Driver: <EMAIL> / password\n";
    }
}
