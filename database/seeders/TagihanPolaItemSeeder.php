<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TagihanPolaItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get or create default satuan for tagihan pola
        $satuanPaket = \App\Models\SatuanDasar::firstOrCreate([
            'nama' => 'Paket'
        ], [
            'kode' => 'PKT',
            'deskripsi' => 'Satuan untuk tagihan pola',
            'created_by' => 1,
        ]);

        // Get or create default kategori for tagihan pola
        $kategoriJasa = \App\Models\ItemKategori::firstOrCreate([
            'nama' => 'Jasa Tagihan Pola'
        ], [
            'kode' => 'JTP',
            'deskripsi' => 'Kategori untuk item tagihan pola',
            'created_by' => 1,
        ]);

        // Create default item for tagihan pola
        $tagihanPolaItem = \App\Models\Item::firstOrCreate([
            'kode' => 'TAGIHAN-POLA-DEFAULT'
        ], [
            'name' => 'Tagihan Pola - Default Item',
            'description' => 'Item default untuk tagihan pola yang tidak menggunakan item dari transaksi',
            'id_item_jenis' => $kategoriJasa->id,
            'id_satuan' => $satuanPaket->id,
            'created_by' => 1, // Assuming admin user ID is 1
        ]);

        $this->command->info("Created tagihan pola default item with ID: {$tagihanPolaItem->id}");
    }
}
