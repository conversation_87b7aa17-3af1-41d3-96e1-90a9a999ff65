<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NotificationSetting;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class NotificationSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Log::info('Starting NotificationSettingSeeder...');

        // Hapus data lama jika ada
        NotificationSetting::truncate();

        // Cari user dengan role manager atau admin yang memiliki nomor HP
        $managers = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['super_admin', 'admin', 'manager']);
        })
            ->whereNotNull('hp')
            ->where('hp', '!=', '')
            ->get();

        if ($managers->isEmpty()) {
            Log::warning('No managers/admins with phone numbers found. Creating notification settings for all users with phone numbers.');

            // Jika tidak ada manager, ambil semua user yang punya HP
            $managers = User::whereNotNull('hp')
                ->where('hp', '!=', '')
                ->limit(3) // Batasi maksimal 3 user untuk menghindari spam
                ->get();
        }

        // Event-event yang membutuhkan notifikasi
        $events = [
            'penjualan_baru' => 'Transaksi Penjualan Baru',
            'penjualan_approved' => 'Transaksi Penjualan Disetujui',
            'sph_baru' => 'SPH Baru',
            'expense_baru' => 'Permintaan Biaya Baru',
            'invoice_baru' => 'Invoice Baru',
            'pembayaran_diterima' => 'Pembayaran Diterima',
        ];

        $createdCount = 0;

        foreach ($managers as $manager) {
            foreach ($events as $eventName => $description) {
                $notificationSetting = NotificationSetting::create([
                    'event_name' => $eventName,
                    'user_id' => $manager->id,
                    'channel' => 'whatsapp',
                    'is_active' => true,
                ]);

                $createdCount++;
                Log::info("Created notification setting: {$eventName} for user {$manager->name} ({$manager->hp})");
            }
        }

        Log::info("NotificationSettingSeeder completed. Created {$createdCount} notification settings for " . $managers->count() . " users.");

        // Tampilkan ringkasan
        $this->command->info("✅ Created {$createdCount} notification settings");
        $this->command->info("📱 Configured for " . $managers->count() . " users:");

        foreach ($managers as $manager) {
            $this->command->info("   - {$manager->name} ({$manager->hp})");
        }

        $this->command->info("🔔 Events configured:");
        foreach ($events as $eventName => $description) {
            $this->command->info("   - {$eventName}: {$description}");
        }
    }
}
