<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Jabatan;

class DirekturJabatanSeeder extends Seeder
{
    /**
     * Run the database seeders.
     * Ensures that "Direktur" position exists in the database
     */
    public function run(): void
    {
        // Check if Direktur position already exists
        $direktur = Jabatan::where('nama', 'Direktur')
            ->orWhere('jabatan_en', 'Director')
            ->first();

        if (!$direktur) {
            Jabatan::create([
                'nama' => 'Direktur',
                'jabatan_en' => 'Director',
                'created_by' => 1, // Assuming admin user has ID 1
            ]);

            $this->command->info('Created Direktur/Director position');
        } else {
            $this->command->info('Direktur/Director position already exists');
        }

        // Also create other common director positions if they don't exist
        $directorPositions = [
            ['nama' => 'Direktur Utama', 'jabatan_en' => 'Chief Executive Director'],
            ['nama' => 'Direktur Operasional', 'jabatan_en' => 'Operations Director'],
            ['nama' => 'Direktur Keuangan', 'jabatan_en' => 'Finance Director'],
            ['nama' => 'Direktur Pemasaran', 'jabatan_en' => 'Marketing Director'],
        ];

        foreach ($directorPositions as $position) {
            $existing = Jabatan::where('nama', $position['nama'])
                ->orWhere('jabatan_en', $position['jabatan_en'])
                ->first();

            if (!$existing) {
                Jabatan::create([
                    'nama' => $position['nama'],
                    'jabatan_en' => $position['jabatan_en'],
                    'created_by' => 1,
                ]);

                $this->command->info("Created {$position['nama']}/{$position['jabatan_en']} position");
            }
        }
    }
}
