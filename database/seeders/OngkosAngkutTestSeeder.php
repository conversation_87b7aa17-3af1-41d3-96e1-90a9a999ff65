<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Pelanggan;
use App\Models\AlamatPelanggan;
use App\Models\TransaksiPenjualan;
use App\Models\PenjualanDetail;
use App\Models\Item;
use App\Models\SatuanDasar;
use App\Models\Tbbm;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\LetterSetting;

class OngkosAngkutTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get or create test user
        $user = User::first() ?? User::factory()->create();

        // Get or create test satuan
        $satuan = SatuanDasar::firstOrCreate(
            ['nama' => 'Liter'],
            ['kode' => 'LTR', 'created_by' => $user->id]
        );

        // Get or create test TBBM
        $tbbm = Tbbm::firstOrCreate(
            ['nama' => 'TBBM Test Ongkos Angkut'],
            [
                'kode' => 'TBBM-ONGKOS-001',
                'alamat' => 'Jl. Test TBBM No. 123',
                'created_by' => $user->id,
            ]
        );

        // Get or create test pelanggan
        $pelanggan = Pelanggan::firstOrCreate(
            ['nama' => 'PT. Test Ongkos Angkut Customer'],
            [
                'kode' => 'CUST-ONGKOS-001',
                'type' => 'corporate',
                'alamat' => 'Jl. Test Customer No. 456',
                'pic_nama' => 'Budi Test',
                'pic_phone' => '081234567890',
                'npwp' => '12.345.678.9-012.345',
                'created_by' => $user->id,
            ]
        );

        // Create alamat pelanggan
        $alamatPelanggan = AlamatPelanggan::firstOrCreate(
            [
                'id_pelanggan' => $pelanggan->id,
                'alamat' => 'Jl. Test Alamat Ongkos Angkut No. 789, Pekanbaru'
            ],
            [
                'is_primary' => true,
            ]
        );

        // Get or create test item for service
        $item = Item::firstOrCreate(
            ['name' => 'Jasa Transportasi BBM - Ongkos Angkut'],
            [
                'kode' => 'JASA-ONGKOS-001',
                'description' => 'Jasa transportasi bahan bakar minyak dengan ongkos angkut',
                'id_satuan' => $satuan->id,
                'created_by' => $user->id,
            ]
        );

        // Get or create letter setting
        $letterSetting = LetterSetting::firstOrCreate(
            ['name' => 'Default Invoice Setting'],
            [
                'city' => 'Pekanbaru',
                'address' => 'Jl. Test Company No. 123',
                'phone_number' => '0761-123456',
                'email' => '<EMAIL>',
                'website' => 'www.testcompany.com',
                'locale' => 'id',
                'is_active' => true,
            ]
        );

        // Create service type transaction with ongkos angkut
        $transaksi = TransaksiPenjualan::create([
            'kode' => 'SO-ONGKOS-' . time(),
            'tipe' => 'jasa', // Service type
            'tanggal' => now(),
            'id_pelanggan' => $pelanggan->id,
            'id_alamat_pelanggan' => $alamatPelanggan->id,
            'nomor_po' => 'PO-ONGKOS-001',
            'top_pembayaran' => 30,
            'id_tbbm' => $tbbm->id,
            'created_by' => $user->id,
        ]);

        // Create penjualan detail
        $penjualanDetail = PenjualanDetail::create([
            'id_transaksi_penjualan' => $transaksi->id,
            'id_item' => $item->id,
            'volume_item' => 5000, // 5000 liter
            'harga_jual' => 0, // Will be filled from ongkos angkut
            'created_by' => $user->id,
        ]);

        // Create invoice with ongkos angkut
        $invoice = Invoice::create([
            'nomor_invoice' => 'INV-ONGKOS-' . time(),
            'id_transaksi' => $transaksi->id,
            'letter_setting_id' => $letterSetting->id,
            'tanggal_invoice' => now(),
            'tanggal_jatuh_tempo' => now()->addDays(30),
            'nama_pelanggan' => $pelanggan->nama,
            'alamat_pelanggan' => $alamatPelanggan->alamat,
            'npwp_pelanggan' => $pelanggan->npwp,
            'subtotal' => 0, // Will be calculated
            'total_pajak' => 0, // Will be calculated
            'total_invoice' => 0, // Will be calculated
            'sisa_tagihan' => 0, // Will be calculated
            'status' => 'draft',
            'created_by' => $user->id,
        ]);

        // Create invoice item with ongkos angkut
        $invoiceItem = InvoiceItem::create([
            'invoice_id' => $invoice->id,
            'item_id' => $item->id,
            'item_name' => $item->name,
            'item_description' => $item->description,
            'quantity' => 5000, // 5000 liter
            'unit' => 'Liter',
            'ongkos_angkut' => 2500, // Rp 2,500 per liter
            'unit_price' => 2500, // Will be set from ongkos_angkut
            'include_ppn' => true,
            'ppn_rate' => 11,
            'notes' => 'Test invoice item dengan ongkos angkut untuk transaksi jasa',
        ]);

        // Update invoice totals
        $invoice->update([
            'subtotal' => $invoiceItem->subtotal,
            'total_pajak' => $invoiceItem->ppn_amount,
            'total_invoice' => $invoiceItem->total_amount,
            'sisa_tagihan' => $invoiceItem->total_amount,
        ]);

        $this->command->info('✅ Test data untuk ongkos angkut berhasil dibuat:');
        $this->command->info("   - Transaksi: {$transaksi->kode} (Tipe: Jasa)");
        $this->command->info("   - Invoice: {$invoice->nomor_invoice}");
        $this->command->info("   - Item: {$item->name}");
        $this->command->info("   - Quantity: {$invoiceItem->quantity} Liter");
        $this->command->info("   - Ongkos Angkut: Rp " . number_format($invoiceItem->ongkos_angkut, 0, ',', '.') . " per liter");
        $this->command->info("   - Total Ongkos Angkut: Rp " . number_format($invoiceItem->total_ongkos_angkut, 0, ',', '.'));
        $this->command->info("   - Total Invoice: Rp " . number_format($invoice->total_invoice, 0, ',', '.'));
    }
}
