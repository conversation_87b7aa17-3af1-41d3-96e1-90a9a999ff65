<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DeliveryOrder;
use App\Models\DeliveryOrderSeal;
use App\Models\User;

class DeliveryOrderSealSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get some delivery orders for testing
        $deliveryOrders = DeliveryOrder::take(5)->get();
        $user = User::first();

        if ($deliveryOrders->isEmpty()) {
            $this->command->info('No DeliveryOrder records found. Please run DeliveryOrderSeeder first.');
            return;
        }

        foreach ($deliveryOrders as $index => $do) {
            // Clear existing seals for this DO
            $do->seals()->delete();

            // Create different seal configurations for testing
            switch ($index % 3) {
                case 0:
                    // Single seal
                    DeliveryOrderSeal::create([
                        'id_delivery_order' => $do->id,
                        'nomor_segel' => 'SGL-' . str_pad($do->id * 100, 6, '0', STR_PAD_LEFT),
                        'jenis_segel' => 'atas',
                        'urutan' => 1,
                        'created_by' => $user->id,
                    ]);
                    break;

                case 1:
                    // Multiple seals - different types
                    $seals = [
                        [
                            'nomor_segel' => 'SGL-A-' . str_pad($do->id * 100, 6, '0', STR_PAD_LEFT),
                            'jenis_segel' => 'atas',
                            'urutan' => 1,
                            'keterangan' => 'Segel atas tangki utama',
                        ],
                        [
                            'nomor_segel' => 'SGL-B-' . str_pad($do->id * 100 + 1, 6, '0', STR_PAD_LEFT),
                            'jenis_segel' => 'bawah',
                            'urutan' => 2,
                            'keterangan' => 'Segel bawah tangki utama',
                        ],
                        [
                            'nomor_segel' => 'SGL-S-' . str_pad($do->id * 100 + 2, 6, '0', STR_PAD_LEFT),
                            'jenis_segel' => 'samping',
                            'urutan' => 3,
                            'keterangan' => 'Segel samping kompartemen',
                        ],
                    ];

                    foreach ($seals as $sealData) {
                        DeliveryOrderSeal::create([
                            'id_delivery_order' => $do->id,
                            'nomor_segel' => $sealData['nomor_segel'],
                            'jenis_segel' => $sealData['jenis_segel'],
                            'urutan' => $sealData['urutan'],
                            'keterangan' => $sealData['keterangan'],
                            'created_by' => $user->id,
                        ]);
                    }
                    break;

                case 2:
                    // Many seals - same type
                    for ($i = 1; $i <= 4; $i++) {
                        DeliveryOrderSeal::create([
                            'id_delivery_order' => $do->id,
                            'nomor_segel' => 'SGL-MT' . $i . '-' . str_pad($do->id * 100 + $i, 6, '0', STR_PAD_LEFT),
                            'jenis_segel' => 'atas',
                            'urutan' => $i,
                            'keterangan' => "Segel kompartemen $i",
                            'created_by' => $user->id,
                        ]);
                    }
                    break;
            }
        }

        $this->command->info('DeliveryOrderSeal seeder completed successfully!');
        $this->command->info('Created seals for ' . $deliveryOrders->count() . ' delivery orders.');
    }
}
