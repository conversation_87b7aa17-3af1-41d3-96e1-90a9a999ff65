<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PostingRule;
use App\Models\PostingRuleEntry;
use App\Models\Akun;

class InvoicePostingRulesSeeder extends Seeder
{
    /**
     * Run the database seeders.
     * Membuat aturan posting untuk penerimaan invoice
     */
    public function run(): void
    {
        // Clear existing invoice posting rules
        $this->clearExistingInvoiceRules();

        // Create invoice posting rules
        $this->createInvoiceIssuanceRules();
        $this->createConditionalInvoiceRules();
        $this->createInvoicePaymentRules();
    }

    private function clearExistingInvoiceRules()
    {
        // Delete existing invoice-related posting rules
        $invoiceRules = PostingRule::whereIn('source_type', ['Invoice', 'InvoicePayment', 'Receipt'])->get();

        foreach ($invoiceRules as $rule) {
            PostingRuleEntry::where('posting_rule_id', $rule->id)->delete();
            $rule->delete();
        }
    }

    private function createInvoiceIssuanceRules()
    {
        // Check if required accounts exist
        $this->ensureRequiredAccountsExist();

        // 1. Posting Rule untuk Penerbitan Invoice (Piutang)
        $invoiceIssuanceRule = PostingRule::create([
            'rule_name' => 'Penerbitan Invoice - Piutang',
            'source_type' => 'Invoice',
            'trigger_condition' => json_encode(['action' => 'create']),
            'description' => 'Aturan posting ketika invoice diterbitkan - mencatat piutang dan penjualan',
            'is_active' => true,
            'priority' => 1,
            'created_by' => 1,
        ]);

        // Debit: Piutang Usaha (Total Invoice)
        $piutangAccount = Akun::where('kode_akun', '1201')->first();
        if (!$piutangAccount) {
            throw new \Exception('Account 1201 (Piutang Usaha) not found. Please run AccountingSeeder first.');
        }

        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceIssuanceRule->id,
            'account_id' => $piutangAccount->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_invoice',
            'description_template' => 'Piutang dari invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        // Credit: Penjualan (Subtotal)
        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceIssuanceRule->id,
            'account_id' => Akun::where('kode_akun', '4101')->first()->id, // Penjualan
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'subtotal',
            'description_template' => 'Penjualan dari invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 2,
        ]);
    }

    private function createConditionalInvoiceRules()
    {
        // 2. Posting Rule untuk PPN Invoice
        $ppnRule = PostingRule::create([
            'rule_name' => 'PPN Invoice',
            'source_type' => 'Invoice',
            'trigger_condition' => json_encode(['include_ppn' => true]),
            'description' => 'Aturan posting untuk PPN ketika invoice diterbitkan',
            'is_active' => true,
            'priority' => 3,
            'created_by' => 1,
        ]);

        // Credit: Hutang PPN
        PostingRuleEntry::create([
            'posting_rule_id' => $ppnRule->id,
            'account_id' => Akun::where('kode_akun', '2102')->first()->id, // Hutang Pajak
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_pajak',
            'description_template' => 'PPN dari invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        // 3. Posting Rule untuk Biaya Operasional
        $operasionalRule = PostingRule::create([
            'rule_name' => 'Biaya Operasional Invoice',
            'source_type' => 'Invoice',
            'trigger_condition' => json_encode(['include_operasional_kerja' => true]),
            'description' => 'Aturan posting untuk biaya operasional ketika invoice diterbitkan',
            'is_active' => true,
            'priority' => 4,
            'created_by' => 1,
        ]);

        // Credit: Pendapatan Operasional
        PostingRuleEntry::create([
            'posting_rule_id' => $operasionalRule->id,
            'account_id' => Akun::where('kode_akun', '4102')->first()->id, // Pendapatan Lain-lain
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'biaya_operasional_kerja',
            'description_template' => 'Pendapatan operasional dari invoice {source.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // 4. Posting Rule untuk PBBKB
        $pbbkbRule = PostingRule::create([
            'rule_name' => 'PBBKB Invoice',
            'source_type' => 'Invoice',
            'trigger_condition' => json_encode(['include_pbbkb' => true]),
            'description' => 'Aturan posting untuk PBBKB ketika invoice diterbitkan',
            'is_active' => true,
            'priority' => 5,
            'created_by' => 1,
        ]);

        // Credit: Pendapatan PBBKB
        PostingRuleEntry::create([
            'posting_rule_id' => $pbbkbRule->id,
            'account_id' => Akun::where('kode_akun', '4102')->first()->id, // Pendapatan Lain-lain
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'biaya_pbbkb',
            'description_template' => 'Pendapatan PBBKB dari invoice {source.nomor_invoice}',
            'sort_order' => 1,
        ]);
    }

    private function createInvoicePaymentRules()
    {
        // 2. Posting Rule untuk Pembayaran Invoice - Kas
        $cashPaymentRule = PostingRule::create([
            'rule_name' => 'Pembayaran Invoice - Kas',
            'source_type' => 'Receipt',
            'trigger_condition' => json_encode(['payment_method' => 'Cash']),
            'description' => 'Aturan posting untuk pembayaran invoice secara tunai',
            'is_active' => true,
            'priority' => 1,
            'created_by' => 1,
        ]);

        // Debit: Kas
        PostingRuleEntry::create([
            'posting_rule_id' => $cashPaymentRule->id,
            'account_id' => Akun::where('kode_akun', '1101')->first()->id, // Kas
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'nominal_bayar',
            'description_template' => 'Pembayaran tunai invoice {source.invoice.nomor_invoice} - {source.invoice.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        // Credit: Piutang Usaha
        PostingRuleEntry::create([
            'posting_rule_id' => $cashPaymentRule->id,
            'account_id' => Akun::where('kode_akun', '1201')->first()->id, // Piutang Usaha
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'nominal_bayar',
            'description_template' => 'Pelunasan piutang invoice {source.invoice.nomor_invoice} - {source.invoice.nama_pelanggan}',
            'sort_order' => 2,
        ]);

        // 3. Posting Rule untuk Pembayaran Invoice - Bank
        $bankPaymentRule = PostingRule::create([
            'rule_name' => 'Pembayaran Invoice - Bank',
            'source_type' => 'Receipt',
            'trigger_condition' => json_encode(['payment_method' => 'Bank']),
            'description' => 'Aturan posting untuk pembayaran invoice melalui bank',
            'is_active' => true,
            'priority' => 2,
            'created_by' => 1,
        ]);

        // Debit: Bank
        PostingRuleEntry::create([
            'posting_rule_id' => $bankPaymentRule->id,
            'account_id' => Akun::where('kode_akun', '1102')->first()->id, // Bank
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'nominal_bayar',
            'description_template' => 'Pembayaran bank invoice {source.invoice.nomor_invoice} - {source.invoice.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        // Credit: Piutang Usaha
        PostingRuleEntry::create([
            'posting_rule_id' => $bankPaymentRule->id,
            'account_id' => Akun::where('kode_akun', '1201')->first()->id, // Piutang Usaha
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'nominal_bayar',
            'description_template' => 'Pelunasan piutang bank invoice {source.invoice.nomor_invoice} - {source.invoice.nama_pelanggan}',
            'sort_order' => 2,
        ]);

        // 4. Posting Rule untuk HPP (Harga Pokok Penjualan)
        $cogsRule = PostingRule::create([
            'rule_name' => 'HPP dari Invoice',
            'source_type' => 'Invoice',
            'trigger_condition' => json_encode(['action' => 'create']),
            'description' => 'Aturan posting untuk mencatat HPP ketika invoice diterbitkan',
            'is_active' => true,
            'priority' => 2,
            'created_by' => 1,
        ]);

        // Debit: Harga Pokok Penjualan
        PostingRuleEntry::create([
            'posting_rule_id' => $cogsRule->id,
            'account_id' => Akun::where('kode_akun', '5101')->first()->id, // HPP
            'dc_type' => 'Debit',
            'amount_type' => 'Calculated',
            'calculation_expression' => 'transaksiPenjualan.penjualanDetails.sum(volume_item * harga_beli)',
            'description_template' => 'HPP dari invoice {source.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // Credit: Persediaan Barang Dagang
        PostingRuleEntry::create([
            'posting_rule_id' => $cogsRule->id,
            'account_id' => Akun::where('kode_akun', '1301')->first()->id, // Persediaan
            'dc_type' => 'Credit',
            'amount_type' => 'Calculated',
            'calculation_expression' => 'transaksiPenjualan.penjualanDetails.sum(volume_item * harga_beli)',
            'description_template' => 'Pengurangan persediaan dari invoice {source.nomor_invoice}',
            'sort_order' => 2,
        ]);
    }

    private function ensureRequiredAccountsExist()
    {
        $requiredAccounts = [
            ['kode_akun' => '1101', 'nama_akun' => 'Kas', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit'],
            ['kode_akun' => '1102', 'nama_akun' => 'Bank', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit'],
            ['kode_akun' => '1201', 'nama_akun' => 'Piutang Usaha', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit'],
            ['kode_akun' => '1301', 'nama_akun' => 'Persediaan Barang Dagang', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit'],
            ['kode_akun' => '2102', 'nama_akun' => 'Hutang Pajak', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit'],
            ['kode_akun' => '4101', 'nama_akun' => 'Penjualan', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit'],
            ['kode_akun' => '4102', 'nama_akun' => 'Pendapatan Lain-lain', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit'],
            ['kode_akun' => '5101', 'nama_akun' => 'Harga Pokok Penjualan', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit'],
        ];

        foreach ($requiredAccounts as $accountData) {
            $existingAccount = Akun::where('kode_akun', $accountData['kode_akun'])->first();

            if (!$existingAccount) {
                Akun::create([
                    'kode_akun' => $accountData['kode_akun'],
                    'nama_akun' => $accountData['nama_akun'],
                    'kategori_akun' => $accountData['kategori_akun'],
                    'tipe_akun' => $accountData['tipe_akun'],
                    'saldo_awal' => 0,
                    'created_by' => 1,
                ]);

                $this->command->info("Created missing account: {$accountData['kode_akun']} - {$accountData['nama_akun']}");
            }
        }
    }
}
