<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PengirimanDriver;
use App\Models\DeliveryEvidence;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;

class DeliveryEvidenceSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get first pengiriman driver record for testing
        $pengirimanDriver = PengirimanDriver::first();
        
        if (!$pengirimanDriver) {
            $this->command->info('No PengirimanDriver records found. Please run PengirimanDriverSeeder first.');
            return;
        }

        $evidenceTypes = [
            'foto_pemutusan_segel_atas' => 'Foto Pemutusan Segel Atas',
            'foto_pemutusan_segel_bawah' => 'Foto Pemutusan Segel Bawah',
            'foto_tera' => 'Foto Tera',
            'foto_sample_bbm' => 'Foto Sample BBM',
            'foto_tangki_mt_kosong' => 'Foto Tangki MT Kosong',
            'foto_mt' => 'Foto MT',
            'foto_pembongkaran' => 'Foto Pembongkaran'
        ];

        // Create sample images for each evidence type
        foreach ($evidenceTypes as $type => $label) {
            // Create 2-3 sample images per type
            $imageCount = rand(2, 3);
            
            for ($i = 1; $i <= $imageCount; $i++) {
                // Create a simple test image
                $imageName = "{$type}_{$i}.jpg";
                $imagePath = storage_path("app/public/test-images/{$imageName}");
                
                // Ensure directory exists
                if (!file_exists(dirname($imagePath))) {
                    mkdir(dirname($imagePath), 0755, true);
                }
                
                // Create a simple colored rectangle as test image
                $this->createTestImage($imagePath, $label . " {$i}");
                
                // Add media to pengiriman driver
                $pengirimanDriver
                    ->addMedia($imagePath)
                    ->toMediaCollection($type);
            }
        }

        $this->command->info('DeliveryEvidence seeder completed successfully!');
    }

    /**
     * Create a simple test image
     */
    private function createTestImage(string $path, string $text): void
    {
        // Create a simple 300x200 colored image with text
        $width = 300;
        $height = 200;
        
        $image = imagecreate($width, $height);
        
        // Colors
        $backgroundColor = imagecolorallocate($image, rand(100, 255), rand(100, 255), rand(100, 255));
        $textColor = imagecolorallocate($image, 0, 0, 0);
        
        // Add text
        $fontSize = 3;
        $textWidth = imagefontwidth($fontSize) * strlen($text);
        $textHeight = imagefontheight($fontSize);
        $x = ($width - $textWidth) / 2;
        $y = ($height - $textHeight) / 2;
        
        imagestring($image, $fontSize, $x, $y, $text, $textColor);
        
        // Save as JPEG
        imagejpeg($image, $path, 90);
        imagedestroy($image);
    }
}
