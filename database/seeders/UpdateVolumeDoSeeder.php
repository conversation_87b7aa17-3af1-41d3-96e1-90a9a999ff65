<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\DeliveryOrder;
use App\Models\PenjualanDetail;

class UpdateVolumeDoSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $this->command->info('Updating volume_do in penjualan_detail from existing delivery orders...');

        // Get all delivery orders that have volume_details
        $deliveryOrders = DeliveryOrder::whereNotNull('volume_details')->get();

        $updated = 0;
        foreach ($deliveryOrders as $do) {
            $volumeDetails = $do->volume_details;
            
            if (is_array($volumeDetails)) {
                foreach ($volumeDetails as $detail) {
                    if (isset($detail['id_item']) && isset($detail['volume'])) {
                        // Find the corresponding penjualan_detail
                        $penjualanDetail = PenjualanDetail::whereHas('transaksiPenjualan', function ($query) use ($do) {
                            $query->where('id', $do->id_transaksi);
                        })
                        ->where('id_item', $detail['id_item'])
                        ->first();

                        if ($penjualanDetail) {
                            $penjualanDetail->volume_do = $detail['volume'];
                            $penjualanDetail->save();
                            $updated++;
                            
                            $this->command->info("Updated volume_do for item {$detail['id_item']} in DO {$do->kode}");
                        }
                    }
                }
            }
        }

        $this->command->info("Successfully updated {$updated} penjualan_detail records with volume_do data.");
    }
}
