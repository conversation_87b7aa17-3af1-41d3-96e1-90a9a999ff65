<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Akun;
use App\Models\PostingRule;
use App\Models\PostingRuleEntry;

class AccountingSeeder extends Seeder
{
    /**
     * Run the database seeders.
     */
    public function run(): void
    {
        // Clear existing data safely
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        PostingRuleEntry::truncate();
        PostingRule::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Update existing akun records to new structure
        $this->updateExistingAccounts();

        // Create basic chart of accounts
        $this->createChartOfAccounts();

        // Create posting rules
        $this->createPostingRules();

        // Create invoice posting rules
        $this->createInvoicePostingRules();
    }

    private function updateExistingAccounts()
    {
        // Update existing accounts to match new structure
        $existingAccounts = Akun::all();

        foreach ($existingAccounts as $account) {
            // Map old tipe_akun to new kategori_akun and tipe_akun
            $kategori = 'Aset';
            $tipe = 'Debit';

            switch ($account->tipe_akun) {
                case 'Aktiva':
                    $kategori = 'Aset';
                    $tipe = 'Debit';
                    break;
                case 'Kewajiban':
                    $kategori = 'Kewajiban';
                    $tipe = 'Kredit';
                    break;
                case 'Modal':
                    $kategori = 'Ekuitas';
                    $tipe = 'Kredit';
                    break;
                case 'Pendapatan':
                    $kategori = 'Pendapatan';
                    $tipe = 'Kredit';
                    break;
                case 'Beban':
                    $kategori = 'Beban';
                    $tipe = 'Debit';
                    break;
            }

            $account->update([
                'kategori_akun' => $kategori,
                'tipe_akun' => $tipe,
                'saldo_awal' => 0,
            ]);
        }
    }

    private function createChartOfAccounts()
    {
        $accounts = [
            // ASET
            ['kode_akun' => '1101', 'nama_akun' => 'Kas', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '1102', 'nama_akun' => 'Bank', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '1201', 'nama_akun' => 'Piutang Usaha', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '1301', 'nama_akun' => 'Persediaan Barang Dagang', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '1401', 'nama_akun' => 'Peralatan', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '1402', 'nama_akun' => 'Akumulasi Penyusutan Peralatan', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],

            // KEWAJIBAN
            ['kode_akun' => '2101', 'nama_akun' => 'Hutang Usaha', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '2102', 'nama_akun' => 'Hutang Pajak', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '2103', 'nama_akun' => 'Hutang Gaji', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],

            // EKUITAS
            ['kode_akun' => '3101', 'nama_akun' => 'Modal Pemilik', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '3201', 'nama_akun' => 'Prive', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],

            // PENDAPATAN
            ['kode_akun' => '4101', 'nama_akun' => 'Penjualan', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],
            ['kode_akun' => '4102', 'nama_akun' => 'Pendapatan Lain-lain', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0],

            // BEBAN
            ['kode_akun' => '5101', 'nama_akun' => 'Harga Pokok Penjualan', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '5201', 'nama_akun' => 'Beban Gaji', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '5202', 'nama_akun' => 'Beban Listrik', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '5203', 'nama_akun' => 'Beban Telepon', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '5204', 'nama_akun' => 'Beban Penyusutan', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
            ['kode_akun' => '5205', 'nama_akun' => 'Beban Lain-lain', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0],
        ];

        foreach ($accounts as $accountData) {
            Akun::firstOrCreate(
                ['kode_akun' => $accountData['kode_akun']],
                array_merge($accountData, ['created_by' => 1])
            );
        }
    }

    private function createPostingRules()
    {
        // Posting Rule untuk Penjualan
        $salesRule = PostingRule::create([
            'rule_name' => 'Penjualan Tunai',
            'source_type' => 'Sale',
            'trigger_condition' => ['payment_method' => 'Cash'],
            'description' => 'Aturan posting untuk penjualan tunai',
            'is_active' => true,
            'priority' => 1,
            'creatphp artisan db:seed --class=InvoicePostingRulesSeeder
ed_by' => 1,
        ]);

        // Entri untuk penjualan tunai
        PostingRuleEntry::create([
            'posting_rule_id' => $salesRule->id,
            'account_id' => Akun::where('kode_akun', '1101')->first()->id, // Kas
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_amount',
            'description_template' => 'Penjualan tunai - {source.transaction_code}',
            'sort_order' => 1,
        ]);

        PostingRuleEntry::create([
            'posting_rule_id' => $salesRule->id,
            'account_id' => Akun::where('kode_akun', '4101')->first()->id, // Penjualan
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_amount',
            'description_template' => 'Penjualan tunai - {source.transaction_code}',
            'sort_order' => 2,
        ]);

        // Posting Rule untuk HPP
        $cogsRule = PostingRule::create([
            'rule_name' => 'Harga Pokok Penjualan',
            'source_type' => 'Sale',
            'trigger_condition' => null,
            'description' => 'Aturan posting untuk mencatat HPP',
            'is_active' => true,
            'priority' => 2,
            'created_by' => 1,
        ]);

        PostingRuleEntry::create([
            'posting_rule_id' => $cogsRule->id,
            'account_id' => Akun::where('kode_akun', '5101')->first()->id, // HPP
            'dc_type' => 'Debit',
            'amount_type' => 'Calculated',
            'calculation_expression' => 'saleItems.sum(quantity * unit_cost)',
            'description_template' => 'HPP - {source.transaction_code}',
            'sort_order' => 1,
        ]);

        PostingRuleEntry::create([
            'posting_rule_id' => $cogsRule->id,
            'account_id' => Akun::where('kode_akun', '1301')->first()->id, // Persediaan
            'dc_type' => 'Credit',
            'amount_type' => 'Calculated',
            'calculation_expression' => 'saleItems.sum(quantity * unit_cost)',
            'description_template' => 'Pengurangan persediaan - {source.transaction_code}',
            'sort_order' => 2,
        ]);
    }

    private function createInvoicePostingRules()
    {
        // Posting Rule untuk Penerimaan Invoice (Piutang)
        $invoiceReceivableRule = PostingRule::create([
            'rule_name' => 'Penerimaan Invoice - Piutang',
            'source_type' => 'Invoice',
            'trigger_condition' => ['payment_method' => 'Credit'],
            'description' => 'Aturan posting untuk penerimaan invoice dengan pembayaran kredit (piutang)',
            'is_active' => true,
            'priority' => 1,
            'created_by' => 1,
        ]);

        // Entri untuk invoice piutang - Debit Piutang Usaha
        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceReceivableRule->id,
            'account_id' => Akun::where('kode_akun', '1201')->first()->id, // Piutang Usaha
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_invoice',
            'description_template' => 'Piutang dari invoice - {source.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // Entri untuk invoice piutang - Credit Penjualan
        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceReceivableRule->id,
            'account_id' => Akun::where('kode_akun', '4101')->first()->id, // Penjualan
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'subtotal',
            'description_template' => 'Penjualan dari invoice - {source.nomor_invoice}',
            'sort_order' => 2,
        ]);

        // Entri untuk PPN (jika ada)
        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceReceivableRule->id,
            'account_id' => Akun::where('kode_akun', '2102')->first()->id, // Hutang Pajak
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_pajak',
            'description_template' => 'PPN dari invoice - {source.nomor_invoice}',
            'sort_order' => 3,
            'condition' => ['include_ppn' => true],
        ]);

        // Posting Rule untuk Pembayaran Invoice (Kas)
        $invoicePaymentRule = PostingRule::create([
            'rule_name' => 'Pembayaran Invoice - Kas',
            'source_type' => 'InvoicePayment',
            'trigger_condition' => ['payment_method' => 'Cash'],
            'description' => 'Aturan posting untuk pembayaran invoice secara tunai',
            'is_active' => true,
            'priority' => 1,
            'created_by' => 1,
        ]);

        // Entri untuk pembayaran invoice - Debit Kas
        PostingRuleEntry::create([
            'posting_rule_id' => $invoicePaymentRule->id,
            'account_id' => Akun::where('kode_akun', '1101')->first()->id, // Kas
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'nominal_bayar',
            'description_template' => 'Pembayaran invoice - {source.invoice.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // Entri untuk pembayaran invoice - Credit Piutang
        PostingRuleEntry::create([
            'posting_rule_id' => $invoicePaymentRule->id,
            'account_id' => Akun::where('kode_akun', '1201')->first()->id, // Piutang Usaha
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'nominal_bayar',
            'description_template' => 'Pelunasan piutang - {source.invoice.nomor_invoice}',
            'sort_order' => 2,
        ]);

        // Posting Rule untuk Pembayaran Invoice (Bank)
        $invoiceBankPaymentRule = PostingRule::create([
            'rule_name' => 'Pembayaran Invoice - Bank',
            'source_type' => 'InvoicePayment',
            'trigger_condition' => ['payment_method' => 'Bank'],
            'description' => 'Aturan posting untuk pembayaran invoice melalui bank',
            'is_active' => true,
            'priority' => 2,
            'created_by' => 1,
        ]);

        // Entri untuk pembayaran bank - Debit Bank
        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceBankPaymentRule->id,
            'account_id' => Akun::where('kode_akun', '1102')->first()->id, // Bank
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'nominal_bayar',
            'description_template' => 'Pembayaran invoice via bank - {source.invoice.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // Entri untuk pembayaran bank - Credit Piutang
        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceBankPaymentRule->id,
            'account_id' => Akun::where('kode_akun', '1201')->first()->id, // Piutang Usaha
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'nominal_bayar',
            'description_template' => 'Pelunasan piutang via bank - {source.invoice.nomor_invoice}',
            'sort_order' => 2,
        ]);

        // Posting Rule untuk Biaya Operasional (jika ada)
        $operationalCostRule = PostingRule::create([
            'rule_name' => 'Biaya Operasional Invoice',
            'source_type' => 'Invoice',
            'trigger_condition' => ['include_operasional_kerja' => true],
            'description' => 'Aturan posting untuk biaya operasional dalam invoice',
            'is_active' => true,
            'priority' => 3,
            'created_by' => 1,
        ]);

        // Entri untuk biaya operasional - Debit Beban Operasional
        PostingRuleEntry::create([
            'posting_rule_id' => $operationalCostRule->id,
            'account_id' => Akun::where('kode_akun', '5205')->first()->id, // Beban Lain-lain
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'biaya_operasional_kerja',
            'description_template' => 'Biaya operasional - {source.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // Posting Rule untuk PBBKB (jika ada)
        $pbbkbRule = PostingRule::create([
            'rule_name' => 'PBBKB Invoice',
            'source_type' => 'Invoice',
            'trigger_condition' => ['include_pbbkb' => true],
            'description' => 'Aturan posting untuk PBBKB dalam invoice',
            'is_active' => true,
            'priority' => 4,
            'created_by' => 1,
        ]);

        // Entri untuk PBBKB - Debit Beban PBBKB
        PostingRuleEntry::create([
            'posting_rule_id' => $pbbkbRule->id,
            'account_id' => Akun::where('kode_akun', '5205')->first()->id, // Beban Lain-lain (atau buat akun khusus PBBKB)
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'biaya_pbbkb',
            'description_template' => 'PBBKB - {source.nomor_invoice}',
            'sort_order' => 1,
        ]);
    }
}
