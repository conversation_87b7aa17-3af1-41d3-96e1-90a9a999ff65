<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DeliveryOrder;
use App\Models\TransaksiPenjualan;
use App\Models\PenjualanDetail;
use App\Models\DeliveryOrderSeal;
use App\Models\Pelanggan;
use App\Models\Item;
use App\Models\User;
use App\Models\Kendaraan;

class FlexiblePrintTestSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $user = User::first();
        $pelanggan = Pelanggan::first();
        $kendaraan = Kendaraan::first();
        $item = Item::first();

        if (!$user || !$pelanggan || !$kendaraan || !$item) {
            $this->command->info('Required data not found. Please run other seeders first.');
            return;
        }

        // Test Case 1: DO dengan 1 item
        $transaksi1 = TransaksiPenjualan::create([
            'kode' => 'SO-TEST-001',
            'tipe' => 'dagang',
            'id_pelanggan' => $pelanggan->id,
            'id_alamat_pelanggan' => '1',
            'tanggal' => now(),
            'created_by' => $user->id,
        ]);

        PenjualanDetail::create([
            'id_transaksi_penjualan' => $transaksi1->id,
            'id_item' => $item->id,
            'volume_item' => 5000,
            'harga_jual' => 10000,
            'created_by' => $user->id,
        ]);

        $do1 = DeliveryOrder::create([
            'kode' => 'DO-TEST-001',
            'id_transaksi' => $transaksi1->id,
            'id_user' => $user->id,
            'id_kendaraan' => $kendaraan->id,
            'tanggal_delivery' => now(),
            'volume_do' => 5000,
            'status_muat' => 'selesai',
            'created_by' => $user->id,
        ]);

        // Tambah 2 segel untuk DO1
        DeliveryOrderSeal::create([
            'id_delivery_order' => $do1->id,
            'nomor_segel' => 'SGL-001-A',
            'jenis_segel' => 'atas',
            'urutan' => 1,
            'created_by' => $user->id,
        ]);

        DeliveryOrderSeal::create([
            'id_delivery_order' => $do1->id,
            'nomor_segel' => 'SGL-001-B',
            'jenis_segel' => 'bawah',
            'urutan' => 2,
            'created_by' => $user->id,
        ]);

        // Test Case 2: DO dengan 3 item
        $transaksi2 = TransaksiPenjualan::create([
            'kode' => 'SO-TEST-002',
            'tipe' => 'dagang',
            'id_pelanggan' => $pelanggan->id,
            'id_alamat_pelanggan' => '1',
            'tanggal' => now(),
            'created_by' => $user->id,
        ]);

        for ($i = 1; $i <= 3; $i++) {
            PenjualanDetail::create([
                'id_transaksi_penjualan' => $transaksi2->id,
                'id_item' => $item->id,
                'volume_item' => 3000,
                'harga_jual' => 10000,
                'created_by' => $user->id,
            ]);
        }

        $do2 = DeliveryOrder::create([
            'kode' => 'DO-TEST-002',
            'id_transaksi' => $transaksi2->id,
            'id_user' => $user->id,
            'id_kendaraan' => $kendaraan->id,
            'tanggal_delivery' => now(),
            'volume_do' => 9000,
            'status_muat' => 'selesai',
            'created_by' => $user->id,
        ]);

        // Tambah 4 segel untuk DO2
        for ($i = 1; $i <= 4; $i++) {
            DeliveryOrderSeal::create([
                'id_delivery_order' => $do2->id,
                'nomor_segel' => "SGL-002-{$i}",
                'jenis_segel' => $i <= 2 ? 'atas' : 'bawah',
                'urutan' => $i,
                'created_by' => $user->id,
            ]);
        }

        // Test Case 3: DO dengan 7 item (banyak item)
        $transaksi3 = TransaksiPenjualan::create([
            'kode' => 'SO-TEST-003',
            'tipe' => 'dagang',
            'id_pelanggan' => $pelanggan->id,
            'id_alamat_pelanggan' => '1',
            'tanggal' => now(),
            'created_by' => $user->id,
        ]);

        for ($i = 1; $i <= 7; $i++) {
            PenjualanDetail::create([
                'id_transaksi_penjualan' => $transaksi3->id,
                'id_item' => $item->id,
                'volume_item' => 1000,
                'harga_jual' => 10000,
                'created_by' => $user->id,
            ]);
        }

        $do3 = DeliveryOrder::create([
            'kode' => 'DO-TEST-003',
            'id_transaksi' => $transaksi3->id,
            'id_user' => $user->id,
            'id_kendaraan' => $kendaraan->id,
            'tanggal_delivery' => now(),
            'volume_do' => 7000,
            'status_muat' => 'selesai',
            'created_by' => $user->id,
        ]);

        // Tambah 6 segel untuk DO3
        for ($i = 1; $i <= 6; $i++) {
            DeliveryOrderSeal::create([
                'id_delivery_order' => $do3->id,
                'nomor_segel' => "SGL-003-{$i}",
                'jenis_segel' => ['atas', 'bawah', 'samping'][$i % 3],
                'urutan' => $i,
                'created_by' => $user->id,
            ]);
        }

        $this->command->info('FlexiblePrintTest seeder completed successfully!');
        $this->command->info('Created 3 test DOs:');
        $this->command->info('- DO-TEST-001: 1 item, 2 seals');
        $this->command->info('- DO-TEST-002: 3 items, 4 seals');
        $this->command->info('- DO-TEST-003: 7 items, 6 seals');
    }
}
