<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Invoice;

class UpdateInvoiceCalculationsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $this->command->info('Updating invoice calculations to use invoice items...');

        $invoices = Invoice::with('invoiceItems')->get();

        $updated = 0;
        foreach ($invoices as $invoice) {
            if ($invoice->invoiceItems->count() > 0) {
                // Calculate totals from invoice items
                $subtotal = $invoice->invoiceItems->sum('subtotal');
                $totalPpn = $invoice->invoiceItems->sum('ppn_amount');
                $totalVolume = $invoice->invoiceItems->sum('quantity');

                // Update operational volume if operational is included
                if ($invoice->include_operasional_kerja) {
                    $invoice->operasional_volume = $totalVolume;
                    
                    // Recalculate operational cost
                    $operationalRate = $invoice->operasional_rate ?? 968;
                    $invoice->biaya_operasional_kerja = $operationalRate * $totalVolume;
                }

                // Update PBBKB volume if PBBKB is included
                if ($invoice->include_pbbkb) {
                    $invoice->pbbkb_volume = $totalVolume;
                    
                    // Recalculate PBBKB cost
                    $pbbkbRate = $invoice->pbbkb_rate ?? 0;
                    $invoice->biaya_pbbkb = $pbbkbRate * ($totalVolume / 10000);
                }

                // Update totals
                $invoice->subtotal = $subtotal;
                $invoice->total_pajak = $totalPpn;

                // Recalculate total invoice
                $totalInvoice = $subtotal + 
                               ($invoice->biaya_ongkos_angkut ?? 0) + 
                               ($invoice->include_operasional_kerja ? ($invoice->biaya_operasional_kerja ?? 0) : 0) + 
                               $totalPpn + 
                               ($invoice->include_pbbkb ? ($invoice->biaya_pbbkb ?? 0) : 0);

                $invoice->total_invoice = $totalInvoice;
                $invoice->save();

                $updated++;
                $this->command->info("Updated invoice {$invoice->nomor_invoice}");
            }
        }

        $this->command->info("Successfully updated {$updated} invoices with new calculations.");
    }
}
