# Invoice Posting Rules Seeder Documentation

## Overview
Seeder untuk membuat aturan posting otomatis ketika melakukan penerimaan invoice dan pembayaran invoice.

## File: `InvoicePostingRulesSeeder.php`

### Aturan Posting yang Dibuat

## 1. Penerbitan Invoice (Piutang)

**Trigger**: Ketika invoice dibuat (`action: create`)
**Source Type**: `Invoice`

### Jurnal Entries:
```
Debit  : Piutang Usaha (1201)         = total_invoice
Credit : Penjualan (4101)             = subtotal
Credit : Hutang <PERSON> (2102)          = total_pajak (jika include_ppn = true)
Credit : Pendapatan Lain-lain (4102)  = biaya_operasional_kerja (jika include_operasional_kerja = true)
Credit : Pendapatan Lain-lain (4102)  = biaya_pbbkb (jika include_pbbkb = true)
```

### Contoh Jurnal:
```
Invoice: INV-001, Total: Rp 123,225,000
- Subtotal BBM: Rp 100,000,000
- Biaya Operasional: Rp 4,840,000  
- PPN 11%: Rp 11,000,000
- PBBKB: Rp 2,545,000
- Biaya Ongkos Angkut: Rp 4,840,000

Jurnal:
Debit  : Piutang Usaha           Rp 123,225,000
Credit : Penjualan                   Rp 100,000,000
Credit : Pendapatan Lain-lain        Rp   4,840,000 (Operasional)
Credit : Pendapatan Lain-lain        Rp   2,545,000 (PBBKB)
Credit : Hutang Pajak                Rp  11,000,000 (PPN)
Credit : Pendapatan Lain-lain        Rp   4,840,000 (Ongkos Angkut)
```

## 2. Pembayaran Invoice - Kas

**Trigger**: Pembayaran invoice dengan metode tunai (`payment_method: Cash`)
**Source Type**: `Receipt`

### Jurnal Entries:
```
Debit  : Kas (1101)           = nominal_bayar
Credit : Piutang Usaha (1201) = nominal_bayar
```

### Contoh Jurnal:
```
Pembayaran: Rp 50,000,000 (sebagian)

Jurnal:
Debit  : Kas                 Rp 50,000,000
Credit : Piutang Usaha           Rp 50,000,000
```

## 3. Pembayaran Invoice - Bank

**Trigger**: Pembayaran invoice melalui bank (`payment_method: Bank`)
**Source Type**: `Receipt`

### Jurnal Entries:
```
Debit  : Bank (1102)          = nominal_bayar
Credit : Piutang Usaha (1201) = nominal_bayar
```

### Contoh Jurnal:
```
Pembayaran: Rp 73,225,000 (pelunasan)

Jurnal:
Debit  : Bank                Rp 73,225,000
Credit : Piutang Usaha           Rp 73,225,000
```

## 4. HPP (Harga Pokok Penjualan)

**Trigger**: Ketika invoice dibuat (`action: create`)
**Source Type**: `Invoice`

### Jurnal Entries:
```
Debit  : Harga Pokok Penjualan (5101) = sum(volume_item * harga_beli)
Credit : Persediaan (1301)            = sum(volume_item * harga_beli)
```

### Contoh Jurnal:
```
HPP BBM: 10,000 liter x Rp 8,500 = Rp 85,000,000

Jurnal:
Debit  : Harga Pokok Penjualan  Rp 85,000,000
Credit : Persediaan                 Rp 85,000,000
```

## Mapping Akun yang Digunakan

| Kode Akun | Nama Akun | Tipe | Penggunaan |
|-----------|-----------|------|------------|
| 1101 | Kas | Aset | Pembayaran tunai |
| 1102 | Bank | Aset | Pembayaran bank |
| 1201 | Piutang Usaha | Aset | Tagihan invoice |
| 1301 | Persediaan | Aset | Pengurangan stok |
| 2102 | Hutang Pajak | Kewajiban | PPN keluaran |
| 4101 | Penjualan | Pendapatan | Penjualan utama |
| 4102 | Pendapatan Lain-lain | Pendapatan | Operasional, PBBKB, Ongkos |
| 5101 | Harga Pokok Penjualan | Beban | HPP |

## Kondisi Posting

### Conditional Entries:
1. **PPN**: Hanya posting jika `include_ppn = true`
2. **Operasional**: Hanya posting jika `include_operasional_kerja = true`
3. **PBBKB**: Hanya posting jika `include_pbbkb = true`

### Source Properties:
- `total_invoice`: Total keseluruhan invoice
- `subtotal`: Subtotal sebelum pajak dan biaya tambahan
- `total_pajak`: Total PPN
- `biaya_operasional_kerja`: Biaya operasional
- `biaya_pbbkb`: Biaya PBBKB
- `nominal_bayar`: Nominal pembayaran

## Flow Lengkap

### 1. Saat Invoice Dibuat:
```
1. Posting piutang dan penjualan
2. Posting HPP dan pengurangan persediaan
3. Posting conditional entries (PPN, Operasional, PBBKB)
```

### 2. Saat Pembayaran Diterima:
```
1. Posting penerimaan kas/bank
2. Posting pengurangan piutang
```

### 3. Hasil Akhir:
```
- Piutang berkurang sesuai pembayaran
- Kas/Bank bertambah sesuai pembayaran
- Penjualan tercatat sesuai subtotal
- HPP dan persediaan terupdate
- PPN dan biaya tambahan tercatat
```

## Cara Menjalankan Seeder

```bash
# Jalankan seeder khusus invoice posting rules
php artisan db:seed --class=InvoicePostingRulesSeeder

# Atau jalankan comprehensive seeder (sudah include)
php artisan db:seed --class=ComprehensiveSeeder
```

## Validasi

Setelah seeder dijalankan, cek:
1. **PostingRule**: 4 rules untuk Invoice dan Receipt
2. **PostingRuleEntry**: Multiple entries per rule
3. **Akun**: Semua akun yang diperlukan sudah ada
4. **Kondisi**: Conditional entries sesuai business logic

## Integration

Seeder ini terintegrasi dengan:
- `ComprehensiveSeeder`: Dipanggil otomatis
- `AccountingSeeder`: Memerlukan chart of accounts
- Auto posting system: Akan digunakan saat invoice dibuat/dibayar
