<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['item_id']);

            // Make item_id nullable
            $table->unsignedBigInteger('item_id')->nullable()->change();

            // Add foreign key constraint with nullable support
            $table->foreign('item_id')->references('id')->on('item')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            // Drop the nullable foreign key constraint
            $table->dropForeign(['item_id']);

            // Make item_id not nullable again
            $table->unsignedBigInteger('item_id')->nullable(false)->change();

            // Add back the original foreign key constraint
            $table->foreign('item_id')->references('id')->on('item')->onDelete('restrict');
        });
    }
};
