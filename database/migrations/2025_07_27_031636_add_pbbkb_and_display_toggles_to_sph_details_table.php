<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sph_details', function (Blueprint $table) {
            // --- UPDATED: The renameColumn line has been removed ---

            // Add the new price component column with the correct spelling
            $table->decimal('pbbkb', 15, 2)->default(0)->after('ppn');

            // Add the new boolean toggles to control display with the correct spelling
            $table->boolean('show_ppn')->default(true)->after('pbbkb');
            $table->boolean('show_oat')->default(true)->after('show_ppn');
            $table->boolean('show_pbbkb')->default(true)->after('show_oat');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sph_details', function (Blueprint $table) {
            // --- UPDATED: The renameColumn line has been removed ---
            $table->dropColumn(['pbbkb', 'show_ppn', 'show_oat', 'show_pbbkb']);
        });
    }
};
