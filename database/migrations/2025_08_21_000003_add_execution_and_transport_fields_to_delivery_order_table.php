<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Add execution type and transport type fields to delivery_order table
     */
    public function up(): void
    {
        Schema::table('delivery_order', function (Blueprint $table) {
            // Tipe pelaksana: internal atau external
            $table->enum('tipe_pelaksana', ['internal', 'external'])
                ->default('internal')
                ->after('status')
                ->comment('Tipe pelaksana: internal (menggunakan kendaraan sendiri) atau external (sewa jasa)');
            
            // Tipe transportasi: kapal atau mobil
            $table->enum('tipe_transportasi', ['kapal', 'mobil'])
                ->default('mobil')
                ->after('tipe_pelaksana')
                ->comment('Tipe transportasi: kapal atau mobil');
            
            // Biaya sewa jasa untuk external services
            $table->decimal('biaya_sewa_jasa', 15, 2)
                ->nullable()
                ->after('tipe_transportasi')
                ->comment('Biaya sewa jasa untuk pelaksana external');
            
            // Index untuk performance
            $table->index(['tipe_pelaksana']);
            $table->index(['tipe_transportasi']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('delivery_order', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['tipe_pelaksana']);
            $table->dropIndex(['tipe_transportasi']);
            
            // Drop columns
            $table->dropColumn([
                'tipe_pelaksana',
                'tipe_transportasi',
                'biaya_sewa_jasa',
            ]);
        });
    }
};
