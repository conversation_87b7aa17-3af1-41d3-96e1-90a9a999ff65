<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate existing no_segel data to new table
        $deliveryOrders = DB::table('delivery_order')
            ->whereNotNull('no_segel')
            ->where('no_segel', '!=', '')
            ->get();

        foreach ($deliveryOrders as $do) {
            // Split multiple seals if they are separated by comma, semicolon, or pipe
            $seals = preg_split('/[,;|]/', $do->no_segel);
            
            foreach ($seals as $index => $seal) {
                $seal = trim($seal);
                if (!empty($seal)) {
                    DB::table('delivery_order_seals')->insert([
                        'id_delivery_order' => $do->id,
                        'nomor_segel' => $seal,
                        'jenis_segel' => 'atas', // Default to 'atas'
                        'urutan' => $index + 1,
                        'created_by' => $do->created_by,
                        'created_at' => $do->created_at,
                        'updated_at' => $do->updated_at,
                    ]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore data back to no_segel field
        $deliveryOrders = DB::table('delivery_order')->get();
        
        foreach ($deliveryOrders as $do) {
            $seals = DB::table('delivery_order_seals')
                ->where('id_delivery_order', $do->id)
                ->orderBy('urutan')
                ->pluck('nomor_segel')
                ->toArray();
                
            if (!empty($seals)) {
                DB::table('delivery_order')
                    ->where('id', $do->id)
                    ->update(['no_segel' => implode(', ', $seals)]);
            }
        }
        
        // Delete all records from delivery_order_seals
        DB::table('delivery_order_seals')->truncate();
    }
};
