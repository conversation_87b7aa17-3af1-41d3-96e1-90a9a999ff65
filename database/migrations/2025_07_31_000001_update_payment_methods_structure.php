<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Add method_display_name column if it doesn't exist
        if (!Schema::hasColumn('payment_methods', 'method_display_name')) {
            Schema::table('payment_methods', function (Blueprint $table) {
                $table->string('method_display_name')->after('method_name')->nullable();
            });
        }

        // Step 2: Update existing records that don't have display names
        $methodsWithoutDisplayName = DB::table('payment_methods')
            ->whereNull('method_display_name')
            ->orWhere('method_display_name', '')
            ->get();
        
        foreach ($methodsWithoutDisplayName as $method) {
            $displayName = match($method->method_name) {
                'bank_bni_utama' => 'BNI Kantor Pusat',
                'bank_mandiri' => 'Mandiri Operasional', 
                'bank_bca' => 'BCA Payroll',
                'bank_bri' => 'BRI Cabang Pekanbaru',
                'kas_kecil_kantor' => 'Kas Kecil Kantor',
                'bank' => ($method->bank_name ?? 'Bank') . ' - ' . ($method->account_name ?? 'Default'),
                default => ucwords(str_replace('_', ' ', $method->method_name))
            };
            
            // Make sure display name is unique
            $counter = 1;
            $originalDisplayName = $displayName;
            while (DB::table('payment_methods')->where('method_display_name', $displayName)->where('id', '!=', $method->id)->exists()) {
                $displayName = $originalDisplayName . ' (' . $counter . ')';
                $counter++;
            }
            
            DB::table('payment_methods')
                ->where('id', $method->id)
                ->update(['method_display_name' => $displayName]);
        }

        // Step 3: Make the field required and handle constraints
        Schema::table('payment_methods', function (Blueprint $table) {
            // Make field not nullable
            $table->string('method_display_name')->nullable(false)->change();
            
            // Try to remove unique constraint from method_name if it exists
            try {
                $table->dropUnique(['method_name']);
            } catch (\Exception) {
                // Constraint might not exist, continue
            }
            
            // Try to add unique constraint to method_display_name
            try {
                $table->unique('method_display_name');
            } catch (\Exception) {
                // Constraint might already exist, continue
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment_methods', function (Blueprint $table) {
            // Try to remove unique constraint from method_display_name
            try {
                $table->dropUnique(['method_display_name']);
            } catch (\Exception) {
                // Constraint might not exist, continue
            }
            
            // Drop the column if it exists
            if (Schema::hasColumn('payment_methods', 'method_display_name')) {
                $table->dropColumn('method_display_name');
            }
            
            // Try to add back unique constraint to method_name
            try {
                $table->unique('method_name');
            } catch (\Exception) {
                // Constraint might already exist, continue
            }
        });
    }
};
