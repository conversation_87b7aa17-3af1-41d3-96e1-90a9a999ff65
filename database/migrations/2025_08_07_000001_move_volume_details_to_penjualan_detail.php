<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, add volume_do column to penjualan_detail table
        Schema::table('penjualan_detail', function (Blueprint $table) {
            $table->float('volume_do')->nullable()->after('volume_item')
                ->comment('Volume yang dikirim dalam DO untuk item ini');
        });

        // Migrate existing data from volume_details to penjualan_detail
        $this->migrateVolumeData();

        // Then, drop volume_details column from delivery_order table
        Schema::table('delivery_order', function (Blueprint $table) {
            $table->dropColumn('volume_details');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add back volume_details column to delivery_order table
        Schema::table('delivery_order', function (Blueprint $table) {
            $table->json('volume_details')->nullable()->after('volume_do')
                ->comment('Detail volume DO dalam format JSON array');
        });

        // Migrate data back from penjualan_detail to volume_details
        $this->migrateVolumeDataBack();

        // Drop volume_do column from penjualan_detail table
        Schema::table('penjualan_detail', function (Blueprint $table) {
            $table->dropColumn('volume_do');
        });
    }

    /**
     * Migrate volume data from delivery_order.volume_details to penjualan_detail.volume_do
     */
    private function migrateVolumeData(): void
    {
        $deliveryOrders = DB::table('delivery_order')
            ->whereNotNull('volume_details')
            ->get();

        foreach ($deliveryOrders as $do) {
            $volumeDetails = json_decode($do->volume_details, true);
            
            if (is_array($volumeDetails)) {
                foreach ($volumeDetails as $detail) {
                    if (isset($detail['id_item']) && isset($detail['volume'])) {
                        // Update penjualan_detail with volume_do
                        DB::table('penjualan_detail')
                            ->join('transaksi_penjualan', 'penjualan_detail.id_transaksi_penjualan', '=', 'transaksi_penjualan.id')
                            ->where('transaksi_penjualan.id', $do->id_transaksi)
                            ->where('penjualan_detail.id_item', $detail['id_item'])
                            ->update([
                                'penjualan_detail.volume_do' => $detail['volume']
                            ]);
                    }
                }
            }
        }
    }

    /**
     * Migrate volume data back from penjualan_detail.volume_do to delivery_order.volume_details
     */
    private function migrateVolumeDataBack(): void
    {
        $deliveryOrders = DB::table('delivery_order')->get();

        foreach ($deliveryOrders as $do) {
            $penjualanDetails = DB::table('penjualan_detail')
                ->join('transaksi_penjualan', 'penjualan_detail.id_transaksi_penjualan', '=', 'transaksi_penjualan.id')
                ->where('transaksi_penjualan.id', $do->id_transaksi)
                ->whereNotNull('penjualan_detail.volume_do')
                ->select('penjualan_detail.id_item', 'penjualan_detail.volume_do', 'penjualan_detail.volume_item')
                ->get();

            if ($penjualanDetails->count() > 0) {
                $volumeDetails = [];
                foreach ($penjualanDetails as $detail) {
                    $volumeDetails[] = [
                        'id_item' => $detail->id_item,
                        'volume' => $detail->volume_do,
                        'max_volume' => $detail->volume_item,
                        'keterangan' => null
                    ];
                }

                DB::table('delivery_order')
                    ->where('id', $do->id)
                    ->update([
                        'volume_details' => json_encode($volumeDetails)
                    ]);
            }
        }
    }
};
