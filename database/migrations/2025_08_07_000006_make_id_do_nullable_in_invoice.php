<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            // Make id_do nullable since invoice now depends on transaksi_penjualan directly
            $table->unsignedBigInteger('id_do')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice', function (Blueprint $table) {
            // Revert id_do to not nullable
            $table->unsignedBigInteger('id_do')->nullable(false)->change();
        });
    }
};
