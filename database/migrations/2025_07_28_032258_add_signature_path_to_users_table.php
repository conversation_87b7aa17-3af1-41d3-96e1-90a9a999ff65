<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * This adds a column to store the path to the user's signature image.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add a nullable string column to store the file path of the signature.
            // It's placed after the 'password' column for logical grouping.
            $table->string('signature_path')->nullable()->after('password');
        });
    }

    /**
     * Reverse the migrations.
     * This safely removes the column if you need to rollback.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('signature_path');
        });
    }
};
