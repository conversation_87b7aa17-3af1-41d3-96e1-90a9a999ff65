<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('invoice_id');
            $table->unsignedBigInteger('item_id');
            $table->string('item_name'); // Store item name for historical purposes
            $table->text('item_description')->nullable(); // Store item description
            $table->decimal('quantity', 15, 2); // Volume/quantity
            $table->string('unit', 50)->default('Liter'); // Unit of measurement
            $table->decimal('unit_price', 15, 2); // Price per unit
            $table->decimal('subtotal', 15, 2); // quantity * unit_price
            $table->decimal('ppn_amount', 15, 2)->default(0); // PPN amount for this item
            $table->decimal('total_amount', 15, 2); // subtotal + ppn_amount
            $table->boolean('include_ppn')->default(true); // Whether PPN is included for this item
            $table->decimal('ppn_rate', 5, 2)->default(11.00); // PPN rate percentage
            $table->text('notes')->nullable(); // Additional notes for this item
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('invoice_id')->references('id')->on('invoice')->onDelete('cascade');
            $table->foreign('item_id')->references('id')->on('item')->onDelete('restrict');
            
            // Indexes for performance
            $table->index(['invoice_id']);
            $table->index(['item_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_items');
    }
};
