<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('delivery_order', function (Blueprint $table) {
            // Print settings
            $table->integer('print_rows_count')->default(5)->after('do_print_status');
            $table->boolean('print_show_empty_rows')->default(true)->after('print_rows_count');
            $table->text('print_custom_notes')->nullable()->after('print_show_empty_rows');
            $table->json('print_signature_settings')->nullable()->after('print_custom_notes');
            
            // Indexes for performance
            $table->index(['print_rows_count']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('delivery_order', function (Blueprint $table) {
            $table->dropIndex(['print_rows_count']);
            
            $table->dropColumn([
                'print_rows_count',
                'print_show_empty_rows', 
                'print_custom_notes',
                'print_signature_settings',
            ]);
        });
    }
};
