<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('penjualan_detail', function (Blueprint $table) {
            // Location fields for each detail item
            $table->json('location')->nullable()->after('harga_jual');
            $table->text('alamat_pengiriman')->nullable()->after('location');
            $table->text('keterangan_lokasi')->nullable()->after('alamat_pengiriman');
            
            // Indexes for performance
            $table->index(['id_transaksi_penjualan']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('penjualan_detail', function (Blueprint $table) {
            $table->dropColumn([
                'location',
                'alamat_pengiriman',
                'keterangan_lokasi',
            ]);
        });
    }
};
