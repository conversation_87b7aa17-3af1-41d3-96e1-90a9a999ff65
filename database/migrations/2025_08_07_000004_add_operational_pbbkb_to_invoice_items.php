<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            // Operational cost fields
            $table->boolean('include_operasional')->default(false)->after('ppn_rate')
                ->comment('Whether operational cost is included for this item');
            $table->decimal('operasional_rate', 15, 2)->default(968.00)->after('include_operasional')
                ->comment('Operational rate per liter');
            $table->decimal('operasional_amount', 15, 2)->default(0)->after('operasional_rate')
                ->comment('Total operational cost for this item');
            
            // PBBKB cost fields
            $table->boolean('include_pbbkb')->default(false)->after('operasional_amount')
                ->comment('Whether PBBKB is included for this item');
            $table->decimal('pbbkb_rate', 15, 2)->default(0)->after('include_pbbkb')
                ->comment('PBBKB rate per liter');
            $table->decimal('pbbkb_amount', 15, 2)->default(0)->after('pbbkb_rate')
                ->comment('Total PBBKB cost for this item');
            
            // Update total_amount to include operational and PBBKB
            // total_amount = subtotal + ppn_amount + operasional_amount + pbbkb_amount
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->dropColumn([
                'include_operasional',
                'operasional_rate', 
                'operasional_amount',
                'include_pbbkb',
                'pbbkb_rate',
                'pbbkb_amount'
            ]);
        });
    }
};
