<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transaksi_penjualan', function (Blueprint $table) {
            // Make kode nullable and remove unique constraint temporarily
            $table->dropUnique(['kode']);
            $table->string('kode', 50)->nullable()->change();

            // Remove unique constraint completely - allow duplicate kode values
            // $table->unique('kode', 'transaksi_penjualan_kode_unique_nullable');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transaksi_penjualan', function (Blueprint $table) {
            // Remove the nullable unique constraint
            $table->dropUnique('transaksi_penjualan_kode_unique_nullable');

            // Make kode non-nullable and add back original unique constraint
            $table->string('kode', 50)->nullable(false)->change();
            $table->unique('kode');
        });
    }
};
