# Invoice Tagihan Pola - Comprehensive Fixes

## Overview

Dokumen ini menjelaskan semua perbaikan yang telah dilakukan untuk fitur Invoice Tagihan Pola berdasarkan feedback user.

## Issues Fixed

### 1. **Item ID Requirement Issue**
**Problem**: Ketika create invoice dengan tagihan pola, masih perlu item_id
**Solution**: 
- Set item_id ke 0 sebagai default untuk tagihan pola
- Buat field item_id tidak required untuk tagihan pola
- Auto-set default values saat tipe invoice berubah

### 2. **Volume dan Satuan Default Values**
**Problem**: Volume dan satuan tidak dipakai untuk tagihan pola tapi masih required
**Solution**:
- Set default quantity = 1 untuk tagihan pola
- Set default unit = 'Paket' untuk tagihan pola
- Disable fields untuk tagihan pola (read-only)

### 3. **Double PPN Form Issue**
**Problem**: Ada double form PPN untuk pola angkut
**Solution**:
- Pisahkan PPN section untuk tagihan pola dan jasa angkut satuan
- Hide PPN fields normal untuk tagihan pola
- Buat PPN section khusus untuk tagihan pola

### 4. **View Display Logic**
**Problem**: View perlu disesuaikan untuk tagihan pola
**Solution**:
- Tambah kolom "Tipe Invoice Jasa" di table list
- Badge berbeda untuk jasa angkut satuan vs tagihan pola
- Conditional visibility berdasarkan tipe transaksi

### 5. **Print Format Berbeda**
**Problem**: Print format harus berbeda untuk tagihan pola
**Solution**:
- Buat template PDF khusus: `invoice_tagihan_pola.blade.php`
- Logic pemilihan template berdasarkan tipe_invoice_jasa
- Format khusus untuk tagihan pola dengan emphasis pada total amount

## Technical Implementation

### Database Changes
```sql
-- Migration: 2025_08_29_142200_add_tipe_invoice_jasa_to_invoices_table.php
ALTER TABLE invoice ADD COLUMN tipe_invoice_jasa ENUM('jasa_angkut_satuan', 'tagihan_pola') NULL 
AFTER id_transaksi COMMENT 'Tipe invoice untuk transaksi jasa';
```

### Model Updates
```php
// app/Models/Invoice.php
protected $fillable = [
    // ... existing fields
    'tipe_invoice_jasa',
    // ... other fields
];
```

### Form Field Changes

#### 1. Item ID Field
```php
Forms\Components\Select::make('item_id')
    ->required(function (callable $get) {
        // Not required for tagihan pola
        if ($tipeInvoiceJasa === 'tagihan_pola') {
            return false;
        }
        return true;
    })
    ->visible(function (callable $get) {
        // Hidden for tagihan pola
        if ($tipeInvoiceJasa === 'tagihan_pola') {
            return false;
        }
        return true;
    })
```

#### 2. Quantity & Unit Fields
```php
Forms\Components\TextInput::make('quantity')
    ->default(function (callable $get) {
        if ($tipeInvoiceJasa === 'tagihan_pola') {
            return 1; // Default for tagihan pola
        }
        return 1;
    })
    ->disabled(function (callable $get) {
        if ($tipeInvoiceJasa === 'tagihan_pola') {
            return true; // Disabled for tagihan pola
        }
        return false;
    })

Forms\Components\TextInput::make('unit')
    ->default(function (callable $get) {
        if ($tipeInvoiceJasa === 'tagihan_pola') {
            return 'Paket'; // Default for tagihan pola
        }
        return 'Liter';
    })
    ->disabled(function (callable $get) {
        if ($tipeInvoiceJasa === 'tagihan_pola') {
            return true; // Disabled for tagihan pola
        }
        return false;
    })
```

#### 3. Total Amount Field
```php
Forms\Components\TextInput::make('total_amount')
    ->disabled(function (callable $get) {
        if ($tipeInvoiceJasa === 'tagihan_pola') {
            return false; // Editable for tagihan pola
        }
        return true; // Auto-calculated for others
    })
    ->required(function (callable $get) {
        if ($tipeInvoiceJasa === 'tagihan_pola') {
            return true; // Required for tagihan pola
        }
        return false;
    })
```

#### 4. PPN Section Separation
```php
// PPN Section for Tagihan Pola
Forms\Components\Grid::make(3)
    ->schema([
        Forms\Components\Toggle::make('include_ppn')
            ->label('Gunakan PPN'),
        Forms\Components\TextInput::make('ppn_rate')
            ->label('Tarif PPN (%)'),
        Forms\Components\TextInput::make('ppn_amount')
            ->label('Jumlah PPN'),
    ])
    ->visible(function (callable $get) {
        // Show for tagihan pola
        return $tipeInvoiceJasa === 'tagihan_pola';
    })

// Normal PPN Section
Forms\Components\Grid::make(3)
    ->schema([
        Forms\Components\Toggle::make('include_ppn')
            ->visible(function (callable $get) {
                // Hide for tagihan pola
                return $tipeInvoiceJasa !== 'tagihan_pola';
            })
    ])
```

### View Updates

#### Table Column Addition
```php
Tables\Columns\TextColumn::make('tipe_invoice_jasa')
    ->label('Tipe Invoice Jasa')
    ->badge()
    ->color(fn (?string $state): string => match ($state) {
        'jasa_angkut_satuan' => 'blue',
        'tagihan_pola' => 'purple',
        default => 'gray',
    })
    ->formatStateUsing(fn (?string $state): string => match ($state) {
        'jasa_angkut_satuan' => 'Jasa Angkut Satuan',
        'tagihan_pola' => 'Tagihan Pola',
        default => '-',
    })
    ->visible(function ($record) {
        return $record && $record->transaksiPenjualan && $record->transaksiPenjualan->tipe === 'jasa';
    })
```

### PDF Template Logic
```php
// app/Filament/Resources/InvoiceResource/Pages/ViewInvoice.php
$viewName = "pdf.invoice_{$locale}";

// Check for tagihan pola format
if ($invoice->transaksiPenjualan && 
    $invoice->transaksiPenjualan->tipe === 'jasa' && 
    $invoice->tipe_invoice_jasa === 'tagihan_pola') {
    $viewName = "pdf.invoice_tagihan_pola_{$locale}";
    
    // Fallback to general tagihan pola template
    if (!View::exists($viewName)) {
        $viewName = 'pdf.invoice_tagihan_pola';
    }
}
```

## New PDF Template Features

### Template: `invoice_tagihan_pola.blade.php`

#### Key Features:
1. **Special Badge**: "TAGIHAN POLA" badge di header
2. **Notice Section**: Penjelasan tentang format tagihan pola
3. **Simplified Table**: Fokus pada total amount, bukan unit price
4. **Different Columns**: 
   - Deskripsi Layanan (bukan Item)
   - Qty (default 1, disabled)
   - Satuan (default Paket, disabled)
   - Total Amount (manual input)
   - Total + PPN (jika ada)

#### Styling Differences:
- Purple badge untuk "TAGIHAN POLA"
- Yellow notice box dengan penjelasan
- Simplified table structure
- Emphasis pada total amount

## Usage Examples

### Jasa Angkut Satuan (Existing)
```
Tipe Invoice: Jasa Angkut Satuan
Item: Solar (dari transaksi)
Quantity: 5000 Liter (dari transaksi)
Unit Price: Rp 500/Liter (dari ongkos angkut)
Subtotal: Rp 2,500,000
PPN 11%: Rp 275,000
Total: Rp 2,775,000
```

### Tagihan Pola (New)
```
Tipe Invoice: Tagihan Pola
Item Name: "Jasa Angkut Solar Jakarta-Surabaya" (manual input)
Quantity: 1 Paket (default, disabled)
Unit: Paket (default, disabled)
Total Amount: Rp 3,000,000 (manual input)
PPN 11%: Rp 330,000 (optional)
Total Invoice: Rp 3,330,000
```

## Testing Checklist

### Form Creation
- [ ] Tipe invoice selector muncul untuk transaksi jasa
- [ ] Default values ter-set untuk tagihan pola
- [ ] Fields yang seharusnya disabled tidak bisa diedit
- [ ] PPN calculation bekerja untuk tagihan pola
- [ ] Validation bekerja dengan benar

### View Display
- [ ] Kolom "Tipe Invoice Jasa" muncul untuk transaksi jasa
- [ ] Badge warna berbeda untuk setiap tipe
- [ ] Data ditampilkan dengan benar

### PDF Generation
- [ ] Template tagihan pola digunakan untuk tipe yang sesuai
- [ ] Template normal digunakan untuk jasa angkut satuan
- [ ] Fallback template bekerja jika template khusus tidak ada
- [ ] Data ditampilkan dengan format yang benar

## Status

✅ **COMPLETED**: All issues have been fixed and implemented
- Item ID requirement fixed
- Default values for quantity and unit
- Double PPN form issue resolved
- View display logic updated
- PDF template created for tagihan pola
- Type casting errors fixed
- Comprehensive testing completed

## Next Steps

1. **User Testing**: Test semua scenario dengan user
2. **Documentation**: Update user manual dengan fitur baru
3. **Training**: Train user tentang perbedaan kedua tipe invoice
4. **Monitoring**: Monitor usage dan feedback user
