<?php

namespace App\Http\Controllers;

use App\Models\UangJalan;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Barryvdh\DomPDF\Facade\Pdf;

class UangJalanReceiptController extends Controller
{
    /**
     * Generate and display the receipt for UangJalan
     */
    public function printReceipt(UangJalan $uangJalan)
    {
        // Load necessary relationships
        $uangJalan->load([
            'deliveryOrder.transaksi.pelanggan',
            'deliveryOrder.kendaraan',
            'user',
            'deliveryOrder.transaksi.penjualanDetails.item'
        ]);

        // Prepare data for the receipt
        $data = [
            'uangJalan' => $uangJalan,
            'deliveryOrder' => $uangJalan->deliveryOrder,
            'transaksi' => $uangJalan->deliveryOrder->transaksi ?? null,
            'pelanggan' => $uangJalan->deliveryOrder->transaksi->pelanggan ?? null,
            'kendaraan' => $uangJalan->deliveryOrder->kendara<PERSON> ?? null,
            'driver' => $uangJalan->user,
            'breakdown' => $uangJalan->getBreakdownArray(),
            'printDate' => now()->format('d F Y'),
            'terbilang' => $this->terbilang($uangJalan->nominal),
        ];

        // Generate PDF
        $pdf = Pdf::loadView('receipts.uang-jalan-receipt', $data);
        $pdf->setPaper('A4', 'portrait');

        // Return PDF for download/print
        return $pdf->stream('Tanda_Terima_Uang_Jalan_' . $uangJalan->deliveryOrder->kode . '.pdf');
    }

    /**
     * Preview the receipt in browser (for development/testing)
     */
    public function previewReceipt(UangJalan $uangJalan)
    {
        // Load necessary relationships
        $uangJalan->load([
            'deliveryOrder.transaksi.pelanggan',
            'deliveryOrder.kendaraan',
            'user',
            'deliveryOrder.transaksi.penjualanDetails.item'
        ]);

        // Prepare data for the receipt
        $data = [
            'uangJalan' => $uangJalan,
            'deliveryOrder' => $uangJalan->deliveryOrder,
            'transaksi' => $uangJalan->deliveryOrder->transaksi ?? null,
            'pelanggan' => $uangJalan->deliveryOrder->transaksi->pelanggan ?? null,
            'kendaraan' => $uangJalan->deliveryOrder->kendaraan ?? null,
            'driver' => $uangJalan->user,
            'breakdown' => $uangJalan->getBreakdownArray(),
            'printDate' => now()->format('d F Y'),
            'terbilang' => $this->terbilang($uangJalan->nominal),
        ];

        // Return view directly for preview
        return view('receipts.uang-jalan-receipt', $data);
    }

    /**
     * Convert number to Indonesian words
     */
    private function terbilang($angka)
    {
        $angka = abs($angka);
        $baca = ["", "satu", "dua", "tiga", "empat", "lima", "enam", "tujuh", "delapan", "sembilan", "sepuluh", "sebelas"];
        $terbilang = "";

        if ($angka < 12) {
            $terbilang = " " . $baca[$angka];
        } else if ($angka < 20) {
            $terbilang = $this->terbilang($angka - 10) . " belas ";
        } else if ($angka < 100) {
            $terbilang = $this->terbilang($angka / 10) . " puluh " . $this->terbilang($angka % 10);
        } else if ($angka < 200) {
            $terbilang = " seratus" . $this->terbilang($angka - 100);
        } else if ($angka < 1000) {
            $terbilang = $this->terbilang($angka / 100) . " ratus " . $this->terbilang($angka % 100);
        } else if ($angka < 2000) {
            $terbilang = " seribu" . $this->terbilang($angka - 1000);
        } else if ($angka < 1000000) {
            $terbilang = $this->terbilang($angka / 1000) . " ribu " . $this->terbilang($angka % 1000);
        } else if ($angka < 1000000000) {
            $terbilang = $this->terbilang($angka / 1000000) . " juta " . $this->terbilang($angka % 1000000);
        }

        return ucwords(trim($terbilang));
    }
}
