<?php

namespace App\Support;

use Carbon\Carbon;

/**
 * A helper class for locale-aware data formatting.
 */
class Formatter
{
    /**
     * Formats a number with locale-specific separators.
     *
     * Usage:
     * Formatter::thousandSeparator(12345.67, 2, 'id') // "12.345,67"
     * Formatter::thousandSeparator(12345.67, 2, 'en') // "12,345.67"
     *
     * @param int|float|null $value The number to format.
     * @param int $decimals The number of decimal places.
     * @param string $locale The locale to use ('id' or 'en').
     * @return string|null
     */
    public static function thousandSeparator(int|float|null $value, int $decimals = 0, string $locale = 'id'): ?string
    {
        if (is_null($value)) {
            return null;
        }

        $decimalSeparator = $locale === 'en' ? '.' : ',';
        $thousandsSeparator = $locale === 'en' ? ',' : '.';

        return number_format($value, $decimals, $decimalSeparator, $thousandsSeparator);
    }

    /**
     * Formats a number into a currency format based on the locale.
     *
     * Usage:
     * Formatter::currency(75000, 'id') // "Rp 75.000"
     * Formatter::currency(75000, 'en') // "$ 75,000.00"
     *
     * @param int|float|null $value The amount to format.
     * @param string $locale The locale to use ('id' or 'en').
     * @return string|null
     */
    public static function currency(int|float|null $value, string $locale = 'id'): ?string
    {
        if (is_null($value)) {
            return null;
        }

        if ($locale === 'en') {
            return '$ ' . self::thousandSeparator($value, 2, 'en');
        }

        // Default to Indonesian format
        return 'Rp ' . self::thousandSeparator($value, 0, 'id');
    }

    /**
     * Legacy function for Indonesian number format for backward compatibility.
     */
    public static function number(int|float|null $value, int $decimals = 0): ?string
    {
        return self::thousandSeparator($value, $decimals, 'id');
    }

    /**
     * Legacy function for Indonesian Rupiah format for backward compatibility.
     */
    public static function rupiah(int|float|null $value): ?string
    {
        return self::currency($value, 'id');
    }

    /**
     * Formats a date into various Indonesian formats.
     */
    public static function date(?string $date, string $locale = 'id'): ?string
    {
        if (!$date) {
            return null;
        }

        if ($locale === 'en') {
            // Standard English format: Month Day, Year
            return Carbon::parse($date)->format('F j, Y');
        }

        // Default Indonesian format: Day Month Year (with translated month)
        return Carbon::parse($date)->locale('id_ID')->translatedFormat('d F Y');
    }

    /**
     * Formats a datetime into various Indonesian formats.
     */
    public static function dateTime(?string $dateTime, string $locale = 'id'): ?string
    {
        if (!$dateTime) {
            return null;
        }

        if ($locale === 'en') {
            return Carbon::parse($dateTime)->format('F j, Y, g:i A');
        }

        return Carbon::parse($dateTime)->locale('id_ID')->translatedFormat('d F Y, H:i');
    }
    
}
