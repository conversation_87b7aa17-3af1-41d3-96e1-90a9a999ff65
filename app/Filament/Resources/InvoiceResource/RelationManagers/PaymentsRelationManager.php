<?php

namespace App\Filament\Resources\InvoiceResource\RelationManagers;

use App\Models\InvoicePayment;
use App\Models\PaymentMethod;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;

class PaymentsRelationManager extends RelationManager
{
    protected static string $relationship = 'payments';

    protected static ?string $title = 'Riwayat Pembayaran';

    protected static ?string $modelLabel = 'Pembayaran';

    protected static ?string $pluralModelLabel = 'Pembayaran';

    protected static ?string $icon = 'heroicon-o-banknotes';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Detail Pembayaran')
                    ->schema([
                        Forms\Components\DateTimePicker::make('payment_date')
                            ->label('Tanggal Pembayaran')
                            ->required()
                            ->default(now()),

                        Forms\Components\TextInput::make('amount')
                            ->label('Jumlah Pembayaran')
                            ->numeric()
                            ->prefix('Rp')
                            ->required()
                            ->rules([
                                'min:1',
                                function () {
                                    return function (string $attribute, $value, \Closure $fail) {
                                        $invoice = $this->ownerRecord;
                                        $totalPaid = $invoice->payments()->accepted()->sum('amount');
                                        $remainingAmount = $invoice->total_invoice - $totalPaid;

                                        if ($value > $remainingAmount) {
                                            $fail("Jumlah pembayaran tidak boleh melebihi sisa tagihan (Rp " . number_format($remainingAmount, 0, ',', '.') . ")");
                                        }
                                    };
                                },
                            ]),

                        Forms\Components\Select::make('payment_method_id')
                            ->label('Metode Pembayaran')
                            ->options(PaymentMethod::active()->pluck('method_display_name', 'id'))
                            ->required()
                            ->searchable(),

                        Forms\Components\TextInput::make('reference_number')
                            ->label('Nomor Referensi')
                            ->helperText('Nomor transfer, cek, atau referensi pembayaran lainnya'),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options(InvoicePayment::getStatusOptions())
                            ->default(InvoicePayment::STATUS_PENDING)
                            ->required(),

                        Forms\Components\Textarea::make('notes')
                            ->label('Catatan')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('reference_number')
            ->columns([
                Tables\Columns\TextColumn::make('payment_date')
                    ->label('Tanggal')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('amount')
                    ->label('Jumlah')
                    ->formatStateUsing(fn($state) => 'Rp ' . number_format($state, 0, ',', '.'))
                    ->sortable(),

                Tables\Columns\TextColumn::make('paymentMethod.method_display_name')
                    ->label('Metode Pembayaran')
                    ->searchable(),

                Tables\Columns\TextColumn::make('reference_number')
                    ->label('Referensi')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'warning' => InvoicePayment::STATUS_PENDING,
                        'success' => InvoicePayment::STATUS_ACCEPTED,
                        'danger' => InvoicePayment::STATUS_REJECTED,
                        'gray' => InvoicePayment::STATUS_CANCELLED,
                    ])
                    ->formatStateUsing(fn($state) => InvoicePayment::getStatusOptions()[$state] ?? $state),

                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Dibuat Oleh')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options(InvoicePayment::getStatusOptions()),

                Tables\Filters\Filter::make('payment_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('payment_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('payment_date', '<=', $date),
                            );
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Tambah Pembayaran')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['created_by'] = auth()->id();
                        return $data;
                    })
                    ->after(function (InvoicePayment $record) {
                        // Update invoice totals after payment creation
                        $this->updateInvoiceTotals();

                        Notification::make()
                            ->title('Pembayaran berhasil ditambahkan')
                            ->success()
                            ->send();
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['updated_by'] = auth()->id();
                        return $data;
                    })
                    ->after(function () {
                        $this->updateInvoiceTotals();
                    }),

                Tables\Actions\Action::make('accept')
                    ->label('Terima')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn(InvoicePayment $record) => $record->status === InvoicePayment::STATUS_PENDING)
                    ->requiresConfirmation()
                    ->action(function (InvoicePayment $record) {
                        $record->update([
                            'status' => InvoicePayment::STATUS_ACCEPTED,
                            'updated_by' => auth()->id(),
                        ]);

                        $this->updateInvoiceTotals();

                        Notification::make()
                            ->title('Pembayaran diterima dan telah diposting ke jurnal')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('reject')
                    ->label('Tolak')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn(InvoicePayment $record) => $record->status === InvoicePayment::STATUS_PENDING)
                    ->requiresConfirmation()
                    ->action(function (InvoicePayment $record) {
                        $record->update([
                            'status' => InvoicePayment::STATUS_REJECTED,
                            'updated_by' => auth()->id(),
                        ]);

                        Notification::make()
                            ->title('Pembayaran ditolak')
                            ->warning()
                            ->send();
                    }),

                Tables\Actions\DeleteAction::make()
                    ->after(function () {
                        $this->updateInvoiceTotals();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->after(function () {
                            $this->updateInvoiceTotals();
                        }),
                ]),
            ])
            ->defaultSort('payment_date', 'desc')
            ->striped()
            ->paginated([10, 25, 50]);
    }

    /**
     * Update invoice payment totals based on accepted payments
     */
    protected function updateInvoiceTotals(): void
    {
        $invoice = $this->ownerRecord;
        $totalPaid = $invoice->payments()->accepted()->sum('amount');
        $remainingAmount = $invoice->total_invoice - $totalPaid;

        // Determine status based on ENUM values: 'draft', 'sent', 'paid', 'partial', 'overdue', 'cancelled'
        $status = $invoice->status; // Keep current status by default

        if ($remainingAmount <= 0) {
            $status = 'paid'; // Fully paid
        } elseif ($totalPaid > 0) {
            $status = 'partial'; // Partial payment - now supported in ENUM
        }

        $invoice->update([
            'total_terbayar' => $totalPaid,
            'sisa_tagihan' => $remainingAmount,
            'status' => $status,
        ]);
    }
}
