<?php

namespace App\Filament\Resources\InvoiceResource\Pages;

use App\Filament\Resources\InvoiceResource;
use App\Models\Invoice;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;
use Filament\Notifications\Notification;



class ViewInvoice extends ViewRecord
{
    protected static string $resource = InvoiceResource::class;

    /**
     * Format currency based on locale without conversion
     */
    private function formatCurrencyByLocale($state, $record): string
    {
        $locale = $record->getLocale();
        if ($locale === 'en') {
            return '$' . number_format($state, 0, ',', '.');
        }
        return $record->formatCurrency($state, false);
    }


    //  infolis untuk menampilkan informasi invoice
    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Invoice')
                    ->schema([
                        TextEntry::make('nomor_invoice')
                            ->icon('heroicon-o-document-text')
                            ->color('primary')
                            ->weight('bold')
                            ->label('Nomor Invoice'),
                        // TextEntry::make('delivery_orders_summary')
                        //     ->label('Delivery Orders')
                        //     ->getStateUsing(function ($record) {
                        //         if (!$record->transaksiPenjualan) {
                        //             return 'Belum ada DO';
                        //         }
                        //
                        //         // Use direct query to avoid relationship issues
                        //         $deliveryOrders = \App\Models\DeliveryOrder::where('id_transaksi', $record->id_transaksi)->get();
                        //
                        //         if ($deliveryOrders->isEmpty()) {
                        //             return 'Belum ada DO';
                        //         }
                        //         return $deliveryOrders->pluck('kode')->implode(', ');
                        //     })
                        //     ->url(fn($record) => $record->deliveryOrdersUrl)
                        //     ->color('primary'),
                        TextEntry::make('transaksiPenjualan.kode')
                            ->label('Nomor SO')
                            ->url(fn($record) => $record->transaksiPenjualan ? route('filament.admin.resources.transaksi-penjualans.view', ['record' => $record->transaksiPenjualan]) : null)
                            ->color('primary'),
                        TextEntry::make('tanggal_invoice')
                            ->label('Tanggal Invoice')
                            ->date('d/m/Y'),
                        TextEntry::make('tanggal_jatuh_tempo')
                            ->label('Tanggal Jatuh Tempo')
                            ->date('d/m/Y')
                            ->color(fn($record) => $record->tanggal_jatuh_tempo && $record->tanggal_jatuh_tempo->isPast() && $record->status !== 'paid' ? 'danger' : 'gray'),
                        TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->color(fn(string $state): string => match ($state) {
                                'draft' => 'secondary',
                                'sent' => 'warning',
                                'paid' => 'success',
                                'overdue' => 'danger',
                                'cancelled' => 'gray',
                                default => 'gray',
                            })
                            ->formatStateUsing(fn(string $state): string => match ($state) {
                                'draft' => 'Draft',
                                'sent' => 'Terkirim',
                                'paid' => 'Lunas',
                                'partial' => 'Sebagian Terbayar',
                                'overdue' => 'Jatuh Tempo',
                                'cancelled' => 'Dibatalkan',
                                default => $state,
                            }),
                        TextEntry::make('letterSetting.name')
                            ->label('Penerbitan Surat')
                            ->placeholder('Tidak Ada')
                            ->badge()
                            ->color('info'),
                        TextEntry::make('letterSetting.locale')
                            ->label('Bahasa Surat')
                            ->placeholder('Tidak Ada')
                            ->formatStateUsing(fn($state) => match ($state) {
                                'id' => 'Bahasa Indonesia',
                                'en' => 'English',
                                default => $state
                            })
                            ->badge()
                            ->color(fn($state) => match ($state) {
                                'id' => 'success',
                                'en' => 'warning',
                                default => 'gray'
                            }),

                        // tampilkan jenis transaksi
                        TextEntry::make('transaksiPenjualan.tipe')
                            ->label('Jenis Transaksi')
                            ->formatStateUsing(fn($state) => match ($state) {
                                'dagang' => 'Penjualan Dagang',
                                'jasa' => 'Jasa',
                                default => $state
                            })
                            ->badge()
                            ->color(fn($state) => match ($state) {
                                'dagang' => 'success',
                                'jasa' => 'warning',
                                default => 'gray'
                            }),
                    ])->columns(3),

                Section::make('Informasi Pelanggan')
                    ->schema([
                        TextEntry::make('nama_pelanggan')
                            ->label('Nama Pelanggan')
                            ->weight('bold'),
                        TextEntry::make('alamat_pelanggan')
                            ->label('Alamat Pelanggan')
                            ->columnSpanFull(),
                        TextEntry::make('npwp_pelanggan')
                            ->label('NPWP Pelanggan'),
                        TextEntry::make('transaksiPenjualan.nomor_po')
                            ->label('Nomor PO'),
                        TextEntry::make('transaksiPenjualan.tanggal')
                            ->label('Tanggal PO')
                            ->date('d/m/Y'),
                    ])->columns(3),

                Section::make('Detail Item Invoice')
                    ->schema([
                        RepeatableEntry::make('invoiceItems')
                            ->label('Item Invoice')
                            ->schema([
                                TextEntry::make('item_name')
                                    ->label('Nama Item'),
                                TextEntry::make('item_description')
                                    ->label('Deskripsi')
                                    ->placeholder('Tidak ada deskripsi'),
                                TextEntry::make('quantity')
                                    ->label('Quantity')
                                    ->numeric(decimalPlaces: 2)
                                    ->suffix(fn($record) => $record->unit ?? 'Unit'),
                                TextEntry::make('unit_price')
                                    ->label('Harga Satuan')
                                    ->formatStateUsing(fn($state, $record) => $this->formatCurrencyByLocale($state, $record->invoice)),
                                TextEntry::make('subtotal')
                                    ->label('Subtotal')
                                    ->formatStateUsing(function ($state, $record) {
                                        $locale = $record->invoice->getLocale();
                                        if ($locale === 'en') {
                                            return '$' . number_format($state, 0, ',', '.');
                                        }
                                        return $record->invoice->formatCurrency($state, false);
                                    }),
                                TextEntry::make('ppn_amount')
                                    ->label('PPN')
                                    ->formatStateUsing(function ($state, $record) {
                                        $locale = $record->invoice->getLocale();
                                        if ($locale === 'en') {
                                            return '$' . number_format($state, 0, ',', '.');
                                        }
                                        return $record->invoice->formatCurrency($state, false);
                                    })
                                    ->visible(fn($record) => $record->include_ppn),
                                TextEntry::make('operasional_amount')
                                    ->label('Operasional')
                                    ->formatStateUsing(function ($state, $record) {
                                        $locale = $record->invoice->getLocale();
                                        if ($locale === 'en') {
                                            return '$' . number_format($state, 0, ',', '.');
                                        }
                                        return $record->invoice->formatCurrency($state, false);
                                    })
                                    ->visible(fn($record) => $record->include_operasional),
                                TextEntry::make('pbbkb_amount')
                                    ->label('PBBKB')
                                    ->formatStateUsing(function ($state, $record) {
                                        $locale = $record->invoice->getLocale();
                                        if ($locale === 'en') {
                                            return '$' . number_format($state, 0, ',', '.');
                                        }
                                        return $record->invoice->formatCurrency($state, false);
                                    })
                                    ->visible(fn($record) => $record->include_pbbkb),
                                TextEntry::make('total_amount')
                                    ->label('Total')
                                    ->formatStateUsing(function ($state, $record) {
                                        $locale = $record->invoice->getLocale();
                                        if ($locale === 'en') {
                                            return '$' . number_format($state, 0, ',', '.');
                                        }
                                        return $record->invoice->formatCurrency($state, false);
                                    })
                                    ->weight('bold'),
                                TextEntry::make('notes')
                                    ->label('Catatan')
                                    ->placeholder('Tidak ada catatan'),
                            ])
                            ->columns(4)
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->visible(fn($record) => $record->invoiceItems && $record->invoiceItems->count() > 0),

                Section::make('Rincian Keuangan')
                    ->schema([
                        TextEntry::make('subtotal')
                            ->label('Subtotal')
                            ->formatStateUsing(fn($state, $record) => $this->formatCurrencyByLocale($state, $record))
                            ->weight('bold'),
                        TextEntry::make('total_pajak')
                            ->label('Total Pajak (PPN 11%)')
                            ->formatStateUsing(fn($state, $record) => $this->formatCurrencyByLocale($state, $record))
                            ->visible(fn($record) => $record->include_ppn),
                        TextEntry::make('biaya_ongkos_angkut')
                            ->label('Biaya Ongkos Angkut')
                            ->formatStateUsing(fn($state, $record) => $this->formatCurrencyByLocale($state, $record))
                            ->visible(fn($record) => $record->biaya_ongkos_angkut > 0),
                        TextEntry::make('biaya_operasional_kerja')
                            ->label('Biaya Operasional Kerja')
                            ->formatStateUsing(fn($state, $record) => $this->formatCurrencyByLocale($state, $record))
                            ->visible(fn($record) => $record->include_operasional_kerja && $record->biaya_operasional_kerja > 0),
                        TextEntry::make('biaya_pbbkb')
                            ->label('Biaya PBBKB')
                            ->formatStateUsing(fn($state, $record) => $this->formatCurrencyByLocale($state, $record))
                            ->visible(fn($record) => $record->include_pbbkb && $record->biaya_pbbkb > 0),
                        TextEntry::make('total_invoice')
                            ->label('Total Invoice')
                            ->formatStateUsing(fn($state, $record) => $this->formatCurrencyByLocale($state, $record))
                            ->weight('bold')
                            ->color('primary')
                            ->size('lg'),
                    ])->columns(3),

                Section::make('Status Pembayaran')
                    ->schema([
                        TextEntry::make('total_terbayar')
                            ->label('Total Terbayar')
                            ->formatStateUsing(fn($state, $record) => $this->formatCurrencyByLocale($state, $record))
                            ->color('success'),
                        TextEntry::make('sisa_tagihan')
                            ->label('Sisa Tagihan')
                            ->formatStateUsing(fn($state, $record) => $this->formatCurrencyByLocale($state, $record))
                            ->color(fn($record) => $record->sisa_tagihan > 0 ? 'warning' : 'success'),
                        TextEntry::make('tanggal_bayar')
                            ->label('Tanggal Bayar')
                            ->date('d/m/Y')
                            ->visible(fn($record) => $record->tanggal_bayar),
                        TextEntry::make('metode_bayar')
                            ->label('Metode Bayar')
                            ->visible(fn($record) => $record->metode_bayar),
                        TextEntry::make('referensi_bayar')
                            ->label('Referensi Bayar')
                            ->visible(fn($record) => $record->referensi_bayar),
                    ])->columns(3),

                Section::make('Catatan')
                    ->schema([
                        TextEntry::make('catatan')
                            ->label('Catatan')
                            ->columnSpanFull()
                            ->visible(fn($record) => $record->catatan),
                    ])
                    ->visible(fn($record) => $record->catatan),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview')
                ->label('Invoice')
                ->color('gray')
                ->icon('heroicon-o-eye')
                ->action(null)
                ->modalContent(function (Invoice $record): \Illuminate\View\View {
                    // Load the record with all necessary relationships including letterSetting
                    $record->load([
                        'transaksiPenjualan.pelanggan.alamatUtama',
                        'transaksiPenjualan.penjualanDetails.item.satuan',
                        'transaksiPenjualan.sph.letterSetting',
                        'createdBy.jabatan',
                        'invoiceItems.item',
                        'letterSetting'
                    ]);

                    // Get ISO certifications
                    $isoCertifications = \App\Models\IsoCertification::where('is_active', true)->get();

                    // Determine the locale from letter setting
                    $locale = $record->getLocale();
                    $viewName = "invoice.invoice-preview-{$locale}";

                    // Check if the view exists, fallback to Indonesian if not
                    if (!View::exists($viewName)) {
                        $viewName = 'invoice.invoice-preview';
                    }

                    return View::make($viewName, [
                        'record' => $record,
                        'isoCertifications' => $isoCertifications
                    ]);
                })
                ->modalHeading("Preview: {$this->record->nomor_invoice}")
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Tutup')
                ->slideOver()
                ->modalWidth('4xl'),

            Actions\EditAction::make(),
            //
            Actions\Action::make('view_so')
                ->label('Lihat SO')
                ->icon('heroicon-o-document-text')
                ->url(fn($record) => $record->transaksiPenjualan ? route('filament.admin.resources.transaksi-penjualans.view', ['record' => $record->transaksiPenjualan]) : null)
                ->visible(fn($record) => $record->transaksiPenjualan !== null)
                ->openUrlInNewTab(false),
            // lihat do
            // lihat do kalau ada
            // Actions\Action::make('view_do')
            //     ->label('Lihat DO')
            //     ->icon('heroicon-o-document-text')
            //     ->color('primary')
            //     ->url(fn($record): string => $record->deliveryOrderUrl)
            //     ->visible(fn($record): bool => $record->deliveryOrderUrl !== null)
            //     ->openUrlInNewTab(false),

            // cetak faktur pajak
            // Actions\Action::make('print_tax_invoice')
            // ->label('Faktur Pajak')
            // ->icon('heroicon-o-printer')
            // ->color('success')
            // ->visible(fn(): bool => Auth::user()?->can('view', $this->record) ?? false)
            // ->action(function () {
            //     try {
            //         // Load the invoice with all necessary relationships
            //         $invoice = Invoice::with([
            //             'transaksiPenjualan.pelanggan.alamatUtama',
            //             'transaksiPenjualan.pelanggan.subdistrict.district.regency',
            //             'transaksiPenjualan.penjualanDetails.item.satuan',
            //             'transaksiPenjualan.sph.letterSetting',
            //             'invoiceItems.item',
            //             'createdBy',
            //             'letterSetting'
            //         ])->find($this->record->id);

            //         if (!$invoice) {
            //             throw new \Exception('Invoice not found');
            //         }

            //         // Generate dynamic filename
            //         $filename = 'FakturPajak_' . str_replace(['/', '\\', ' '], '_', $invoice->nomor_invoice) . '_' . now()->format('Ymd_His') . '.pdf';

            //         // Get logo as base64
            //         $logoPath = public_path('images/lrp.png');
            //         $logoBase64 = '';

            //         if (File::exists($logoPath)) {
            //             $logoBase64 = base64_encode(File::get($logoPath));
            //         }

            //         // Determine the locale from letter setting
            //         $locale = $invoice->getLocale();
            //         $viewName = "pdf.tax_invoice_from_invoice_{$locale}";

            //         // Check if the view exists, fallback to Indonesian if not
            //         if (!View::exists($viewName)) {
            //             $viewName = 'pdf.tax_invoice_from_invoice';
            //         }

            //         // Load the PDF view with the record data
            //         $pdf = Pdf::loadView($viewName, [
            //             'record' => $invoice,
            //             'logoBase64' => $logoBase64
            //         ])
            //             ->setPaper('a4', 'portrait')
            //             ->setOptions([
            //                 'isHtml5ParserEnabled' => true,
            //                 'isPhpEnabled' => true,
            //                 'defaultFont' => 'Arial',
            //                 'dpi' => 150,
            //                 'defaultPaperSize' => 'a4',
            //                 'chroot' => public_path(),
            //             ]);

            //         return response()->streamDownload(function () use ($pdf) {
            //             echo $pdf->output();
            //         }, $filename, [
            //             'Content-Type' => 'application/pdf',
            //         ]);
            //     } catch (\Exception $e) {
            //         Log::error('Error generating tax invoice PDF: ' . $e->getMessage());

            //         \Filament\Notifications\Notification::make()
            //             ->title('Error')
            //             ->body('Gagal membuat PDF Faktur Pajak: ' . $e->getMessage())
            //             ->danger()
            //             ->send();
            //     }
            // }),

            // cetak receipt
            Actions\Action::make('print_receipt')
                ->label('Receipt')
                ->icon('heroicon-o-printer')
                ->color('success')
                ->visible(fn(): bool => Auth::user()?->can('view', $this->record) ?? false)
                ->action(function () {
                    try {
                        // Load the invoice with all necessary relationships including letterSetting
                        $invoice = Invoice::with([
                            'transaksiPenjualan.pelanggan',
                            'transaksiPenjualan.alamatPelanggan',
                            'transaksiPenjualan.deliveryOrders',
                            'transaksiPenjualan.penjualanDetails.item.satuan',
                            'transaksiPenjualan.sph.letterSetting',
                            'createdBy',
                            'invoiceItems.item',
                            'receipts',
                            'taxInvoice',
                            'letterSetting'
                        ])->find($this->record->id);

                        if (!$invoice) {
                            throw new \Exception('Invoice not found');
                        }

                        // Determine the locale from letter setting
                        $locale = $invoice->getLocale();

                        // Generate dynamic filename based on locale
                        $prefix = $locale === 'en' ? 'Receipt' : 'Kwitansi';
                        $filename = $prefix . '_' . str_replace(['/', '\\', ' '], '_', $invoice->nomor_invoice) . '_' . now()->format('Ymd_His') . '.pdf';

                        // Get logo as base64
                        $logoPath = public_path('images/lrp.png');
                        $logoBase64 = '';

                        if (File::exists($logoPath)) {
                            $logoBase64 = base64_encode(File::get($logoPath));
                        }

                        // Determine the view name based on locale
                        $viewName = $locale === 'en' ? 'pdf.receipt_en' : 'pdf.receipt';

                        // Load the PDF view with the record data
                        $pdf = Pdf::loadView($viewName, [
                            'record' => $invoice,
                            'logoBase64' => $logoBase64
                        ])
                            ->setPaper('a4', 'portrait')
                            ->setOptions([
                                'isHtml5ParserEnabled' => true,
                                'isPhpEnabled' => true,
                                'defaultFont' => 'Arial',
                                'defaultPaperSize' => 'a4',
                                'chroot' => public_path(),
                            ]);

                        // Stream the PDF as a download
                        return response()->streamDownload(function () use ($pdf) {
                            echo $pdf->output();
                        }, $filename, [
                            'Content-Type' => 'application/pdf',
                            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
                        ]);
                    } catch (\Exception $e) {
                        // Log the error for debugging
                        Log::error('Failed to generate Receipt PDF: ' . $e->getMessage());
                        Log::error('Receipt PDF Error Stack Trace: ' . $e->getTraceAsString());
                        Log::error('Receipt PDF Error Context: ', [
                            'invoice_id' => $this->record->id,
                            'user_id' => Auth::id(),
                        ]);

                        // Show notification to user
                        \Filament\Notifications\Notification::make()
                            ->title('Error generating PDF')
                            ->body('Failed to generate PDF: ' . $e->getMessage())
                            ->danger()
                            ->send();

                        return;
                    }
                }),

            Actions\Action::make('print_both')
                ->label('Print Receipt & Invoice')
                ->icon('heroicon-o-document-duplicate')
                ->color('warning')
                ->visible(fn(): bool => Auth::user()?->can('view', $this->record) ?? false)
                ->action(function () {
                    try {
                        // Load the invoice with all necessary relationships including letterSetting
                        $invoice = Invoice::with([
                            'transaksiPenjualan.pelanggan',
                            'transaksiPenjualan.alamatPelanggan',
                            'transaksiPenjualan.deliveryOrders',
                            'transaksiPenjualan.penjualanDetails.item.satuan',
                            'transaksiPenjualan.sph.letterSetting',
                            'createdBy',
                            'invoiceItems.item',
                            'receipts',
                            'taxInvoice',
                            'letterSetting'
                        ])->find($this->record->id);

                        if (!$invoice) {
                            throw new \Exception('Invoice not found');
                        }

                        // Get logo as base64
                        $logoPath = public_path('images/lrp.png');
                        $logoBase64 = '';
                        if (File::exists($logoPath)) {
                            $logoBase64 = base64_encode(File::get($logoPath));
                        }

                        // Determine the locale from letter setting
                        $locale = $invoice->letterSetting?->locale ?? 'id';

                        // Create combined PDF using the combined template
                        $combinedPdf = Pdf::loadView('pdf.combined_receipt_invoice', [
                            'record' => $invoice,
                            'logoBase64' => $logoBase64,
                            'locale' => $locale
                        ])->setPaper('a4', 'portrait')->setOptions([
                            'isHtml5ParserEnabled' => true,
                            'isPhpEnabled' => true,
                            'defaultFont' => 'Arial'
                        ]);

                        // Generate filename
                        $filename = 'Receipt_Invoice_' . $invoice->nomor_invoice . '_' . now()->format('Ymd_His') . '.pdf';

                        // Stream the combined PDF as a download
                        return response()->streamDownload(function () use ($combinedPdf) {
                            echo $combinedPdf->output();
                        }, $filename);
                    } catch (\Exception $e) {
                        Log::error('Failed to generate combined PDF: ' . $e->getMessage());

                        Notification::make()
                            ->title('Error generating combined PDF')
                            ->body('Failed to generate combined PDF: ' . $e->getMessage())
                            ->danger()
                            ->send();

                        return null;
                    }
                }),

            Actions\Action::make('print_pdf')
                ->label('Invoice')
                ->icon('heroicon-o-printer')
                ->color('success')
                ->visible(fn(): bool => Auth::user()?->can('view', $this->record) ?? false)
                ->action(function () {
                    try {
                        // Load the invoice with all necessary relationships including letterSetting
                        $invoice = Invoice::with([
                            'transaksiPenjualan.pelanggan',
                            'transaksiPenjualan.penjualanDetails.item.satuan',
                            'transaksiPenjualan.sph.letterSetting',
                            'createdBy',
                            'invoiceItems.item',
                            'receipts',
                            'taxInvoice',
                            'letterSetting'
                        ])->find($this->record->id);

                        if (!$invoice) {
                            throw new \Exception('Invoice not found');
                        }

                        // Generate dynamic filename
                        $filename = 'Invoice_' . str_replace(['/', '\\', ' '], '_', $invoice->nomor_invoice) . '_' . now()->format('Ymd_His') . '.pdf';

                        // Get logo as base64
                        $logoPath = public_path('images/lrp.png');
                        $logoBase64 = '';

                        if (File::exists($logoPath)) {
                            $logoBase64 = base64_encode(File::get($logoPath));
                        }

                        // Determine the locale from letter setting
                        $locale = $invoice->getLocale();

                        // Determine view name based on invoice type for service transactions
                        $viewName = "pdf.invoice_{$locale}";

                        // Check for tagihan pola format
                        if (
                            $invoice->transaksiPenjualan &&
                            $invoice->transaksiPenjualan->tipe === 'jasa' &&
                            $invoice->tipe_invoice_jasa === 'tagihan_pola'
                        ) {
                            $viewName = "pdf.invoice_tagihan_pola_{$locale}";

                            // Fallback to general tagihan pola template if locale-specific doesn't exist
                            if (!View::exists($viewName)) {
                                $viewName = 'pdf.invoice_tagihan_pola';
                            }
                        }

                        // Check if the view exists, fallback to Indonesian if not
                        if (!View::exists($viewName)) {
                            $viewName = 'pdf.invoice';
                        }

                        // Load the PDF view with the record data
                        $pdf = Pdf::loadView($viewName, [
                            'record' => $invoice,
                            'logoBase64' => $logoBase64
                        ])
                            ->setPaper('a4', 'portrait')
                            ->setOptions([
                                'isHtml5ParserEnabled' => true,
                                'isPhpEnabled' => true,
                                'defaultFont' => 'Arial',
                                'defaultPaperSize' => 'a4',
                                'chroot' => public_path(),
                            ]);

                        // Stream the PDF as a download
                        return response()->streamDownload(function () use ($pdf) {
                            echo $pdf->output();
                        }, $filename, [
                            'Content-Type' => 'application/pdf',
                            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
                        ]);
                    } catch (\Exception $e) {
                        // Log the error for debugging
                        Log::error('Failed to generate Invoice PDF: ' . $e->getMessage());
                        Log::error('Invoice PDF Error Stack Trace: ' . $e->getTraceAsString());
                        Log::error('Invoice PDF Error Context: ', [
                            'invoice_id' => $this->record->id,
                            'user_id' => Auth::id(),
                        ]);

                        // Show notification to user
                        \Filament\Notifications\Notification::make()
                            ->title('Error generating PDF')
                            ->body('Failed to generate PDF: ' . $e->getMessage())
                            ->danger()
                            ->send();

                        return;
                    }
                }),
        ];
    }
}
