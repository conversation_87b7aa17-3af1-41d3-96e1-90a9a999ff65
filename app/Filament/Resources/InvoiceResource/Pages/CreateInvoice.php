<?php

namespace App\Filament\Resources\InvoiceResource\Pages;

use App\Filament\Resources\InvoiceResource;
use Filament\Resources\Pages\CreateRecord;

class CreateInvoice extends CreateRecord
{
    protected static string $resource = InvoiceResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Auto-populate from URL parameter when creating new invoice
        if (request()->has('id_transaksi')) {
            $idTransaksi = request()->get('id_transaksi');
            $transaksi = \App\Models\TransaksiPenjualan::with('pelanggan', 'alamatPelanggan', 'penjualanDetails.item')->find($idTransaksi);

            if ($transaksi) {
                $data['id_transaksi'] = $transaksi->id;

                // Auto-populate customer data
                if ($transaksi->pelanggan) {
                    $data['nama_pelanggan'] = $transaksi->pelanggan->nama;
                    $data['npwp_pelanggan'] = $transaksi->pelanggan->npwp;
                }

                // Auto-populate address
                if ($transaksi->alamatPelanggan) {
                    $alamatLengkap = collect([
                        $transaksi->alamatPelanggan->alamat,
                        $transaksi->alamatPelanggan->kelurahan,
                        $transaksi->alamatPelanggan->kecamatan,
                        $transaksi->alamatPelanggan->kota,
                        $transaksi->alamatPelanggan->provinsi,
                        $transaksi->alamatPelanggan->kode_pos
                    ])->filter()->implode(', ');

                    $data['alamat_pelanggan'] = $alamatLengkap;
                } else {
                    $data['alamat_pelanggan'] = $transaksi->pelanggan->alamat ?? '';
                }

                // Calculate subtotal from penjualan details
                $subtotal = 0;
                $totalVolume = 0;

                foreach ($transaksi->penjualanDetails as $detail) {
                    $itemTotal = ($detail->volume_do ?? $detail->volume_item) * $detail->harga_jual;
                    $subtotal += $itemTotal;
                    $totalVolume += ($detail->volume_do ?? $detail->volume_item);
                }

                // Set calculated values
                $data['subtotal'] = $subtotal;
                $data['operasional_volume'] = $totalVolume;
                $data['pbbkb_volume'] = $totalVolume;

                // Set default dates
                $data['tanggal_invoice'] = now();
                $data['tanggal_jatuh_tempo'] = now()->addDays(30);

                // Set default status
                $data['status'] = 'draft';
            }
        }

        return $data;
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Remove id_do if present (no longer needed)
        unset($data['id_do']);

        // Set created_by
        $data['created_by'] = \Illuminate\Support\Facades\Auth::id();

        return $data;
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            $this->getCreateAnotherFormAction(),
            $this->getCancelFormAction(),
        ];
    }

    protected function afterCreate(): void
    {
        // Recalculate all totals after creating to ensure accuracy
        $this->recalculateInvoiceTotals();

        // Auto posting journal entry when invoice is created
        $this->record->createJournalEntry();
    }

    /**
     * Recalculate invoice totals from database records
     */
    protected function recalculateInvoiceTotals(): void
    {
        $record = $this->record;

        // First, recalculate individual item totals
        foreach ($record->invoiceItems as $item) {
            $quantity = (float) $item->quantity;
            $unitPrice = (float) $item->unit_price;

            // Calculate subtotal
            $subtotal = $quantity * $unitPrice;

            // Calculate PPN
            $ppnAmount = 0;
            if ($item->include_ppn) {
                $ppnRate = (float) ($item->ppn_rate ?? 11);
                $ppnAmount = $subtotal * ($ppnRate / 100);
            }

            // Calculate operational
            $operasionalAmount = 0;
            if ($item->include_operasional) {
                $operasionalRate = (float) ($item->operasional_rate ?? 0);
                $operasionalAmount = $quantity * $operasionalRate;
            }

            // Calculate PBBKB
            $pbbkbAmount = 0;
            if ($item->include_pbbkb) {
                $pbbkbRate = (float) ($item->pbbkb_rate ?? 0);
                $pbbkbAmount = $quantity * $pbbkbRate;
            }

            // Calculate total amount
            $totalAmount = $subtotal + $ppnAmount + $operasionalAmount + $pbbkbAmount;

            // Update item totals
            $item->update([
                'subtotal' => $subtotal,
                'ppn_amount' => $ppnAmount,
                'operasional_amount' => $operasionalAmount,
                'pbbkb_amount' => $pbbkbAmount,
                'total_amount' => $totalAmount,
            ]);
        }

        // Refresh the relationship to get updated data
        $record->refresh();
        $record->load('invoiceItems');

        // Calculate invoice totals from updated items
        $totalSubtotal = $record->invoiceItems->sum('subtotal');
        $totalPpn = $record->invoiceItems->sum('ppn_amount');
        $totalOperasional = $record->invoiceItems->sum('operasional_amount');
        $totalPbbkb = $record->invoiceItems->sum('pbbkb_amount');
        $totalVolume = $record->invoiceItems->sum('quantity');

        // Calculate final total
        $biayaOngkos = (float) $record->biaya_ongkos_angkut;
        $totalInvoice = $totalSubtotal + $biayaOngkos + $totalOperasional + $totalPpn + $totalPbbkb;

        // Update invoice totals
        $record->update([
            'subtotal' => $totalSubtotal,
            'total_pajak' => $totalPpn,
            'biaya_operasional_kerja' => $totalOperasional,
            'biaya_pbbkb' => $totalPbbkb,
            'operasional_volume' => $totalVolume,
            'pbbkb_volume' => $totalVolume,
            'total_invoice' => $totalInvoice,
        ]);
    }
}
