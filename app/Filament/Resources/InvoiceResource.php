<?php

namespace App\Filament\Resources;

use App\Filament\Resources\InvoiceResource\Pages;
use App\Models\Invoice;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Filament\Notifications\Notification;
use App\Filament\Resources\InvoiceResource\RelationManagers;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Manajemen Keuangan';

    protected static ?string $navigationLabel = 'Invoice';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Invoice')
                    ->schema([
                        Forms\Components\TextInput::make('nomor_invoice')
                            ->label('Nomor Invoice')
                            ->disabled()
                            ->dehydrated()
                            ->helperText('Nomor invoice akan di-generate otomatis')
                            ->placeholder('Auto-generate saat simpan'),

                        Forms\Components\Toggle::make('is_standalone')
                            ->label('Invoice Standalone')
                            ->helperText('Aktifkan untuk membuat invoice yang tidak terkait dengan transaksi penjualan')
                            ->reactive()
                            ->default(false)
                            ->afterStateUpdated(function (callable $set, $state) {
                                if ($state) {
                                    // Clear transaction-related fields when switching to standalone
                                    $set('id_transaksi', null);
                                }
                            }),

                        Forms\Components\Select::make('id_transaksi')
                            ->label('Transaksi Penjualan')
                            ->placeholder('Pilih Transaksi Penjualan')
                            ->relationship('transaksiPenjualan', 'id')
                            ->getOptionLabelFromRecordUsing(fn($record) => $record->nomor_transaksi . ' - ' . $record->pelanggan->nama)
                            ->searchable(['id'])
                            ->preload()
                            ->required(fn(callable $get) => !$get('is_standalone'))
                            ->visible(fn(callable $get) => !$get('is_standalone'))
                            // Auto-populated from URL parameter
                            ->default(function () {
                                return request()->query('id_transaksi', null);
                            })
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                if ($state) {
                                    static::populateFromTransaksiPenjualan($set, $get, $state);
                                }
                            })
                            ->afterStateHydrated(function (callable $set, callable $get, $state) {
                                // Auto-populate when form loads with default value from URL
                                if ($state) {
                                    // Set flag for JavaScript to trigger populate after form is ready
                                    $set('_auto_populate_needed', $state);
                                }
                            })
                            ->helperText('Pilih transaksi penjualan untuk auto-populate data'),

                        Forms\Components\Select::make('letter_setting_id')
                            ->label('Penerbitan Surat')
                            ->relationship('letterSetting', 'name', fn($query) => $query->where('is_active', true))
                            ->searchable()
                            ->preload()
                            ->helperText('Format surat akan diambil otomatis dari SPH atau menggunakan default')
                            ->required(),

                        // Trigger calculation when form loads
                        Forms\Components\Hidden::make('_calculation_trigger')
                            ->default('trigger')
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                if ($state === 'trigger') {
                                    // Trigger initial calculations
                                    static::updateTotals($set, $get);
                                    $set('_calculation_trigger', null);
                                }
                            }),

                        Forms\Components\DateTimePicker::make('tanggal_invoice')
                            ->label('Tanggal Invoice')
                            ->required()
                            ->default(now()),

                        Forms\Components\DateTimePicker::make('tanggal_jatuh_tempo')
                            ->label('Tanggal Jatuh Tempo')
                            ->required()
                            ->default(fn() => now()->addDays(30)),
                    ])->columns(2),

                Forms\Components\Section::make('Informasi Pelanggan Standalone')
                    ->schema([
                        Forms\Components\TextInput::make('standalone_customer_name')
                            ->label('Nama Pelanggan')
                            ->required(fn(callable $get) => $get('is_standalone'))
                            ->placeholder('Masukkan nama pelanggan'),

                        Forms\Components\TextInput::make('standalone_customer_address')
                            ->label('Alamat Pelanggan')
                            ->placeholder('Masukkan alamat pelanggan'),

                        Forms\Components\TextInput::make('standalone_customer_phone')
                            ->label('Telepon Pelanggan')
                            ->placeholder('Masukkan nomor telepon'),

                        Forms\Components\TextInput::make('standalone_customer_email')
                            ->label('Email Pelanggan')
                            ->email()
                            ->placeholder('Masukkan email pelanggan'),
                    ])
                    ->visible(fn(callable $get) => $get('is_standalone'))
                    ->columns(2),

                Forms\Components\Section::make('Informasi Transaksi')
                    ->schema([
                        Forms\Components\Placeholder::make('transaction_info')
                            ->label('')
                            ->live()
                            ->content(function (callable $get) {
                                $transaksiId = $get('id_transaksi');
                                if (!$transaksiId) {
                                    return 'Pilih Transaksi Penjualan untuk melihat informasi transaksi';
                                }

                                try {
                                    $transaksi = \App\Models\TransaksiPenjualan::with([
                                        'pelanggan',
                                        'alamatPelanggan',
                                        'tbbm',
                                        'penjualanDetails.item'
                                    ])->find($transaksiId);

                                    if (!$transaksi) {
                                        return 'Data transaksi tidak ditemukan';
                                    }

                                    $tipeLabel = $transaksi->tipe === 'jasa' ? 'Jasa' : 'Dagang';
                                    $tipeColor = $transaksi->tipe === 'jasa' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800';

                                    $html = '<div class="space-y-4">';

                                    // Header dengan tipe transaksi
                                    $html .= '<div class="flex items-center justify-between">';
                                    $html .= '<h3 class="text-lg font-semibold text-gray-900">Informasi Transaksi</h3>';
                                    $html .= '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' . $tipeColor . '">';
                                    $html .= 'Transaksi ' . $tipeLabel;
                                    $html .= '</span>';
                                    $html .= '</div>';

                                    // Detail transaksi
                                    $html .= '<div class="grid grid-cols-2 gap-4">';

                                    $html .= '<div class="space-y-2">';
                                    $html .= '<div><span class="font-medium text-gray-700">Nomor SO:</span> <span class="text-gray-900">' . ($transaksi->kode ?? '-') . '</span></div>';
                                    $html .= '<div><span class="font-medium text-gray-700">Nomor PO:</span> <span class="text-gray-900">' . ($transaksi->nomor_po ?? '-') . '</span></div>';
                                    $html .= '<div><span class="font-medium text-gray-700">Tanggal:</span> <span class="text-gray-900">' . $transaksi->tanggal->format('d/m/Y H:i') . '</span></div>';
                                    $html .= '</div>';

                                    $html .= '<div class="space-y-2">';
                                    $html .= '<div><span class="font-medium text-gray-700">TBBM:</span> <span class="text-gray-900">' . ($transaksi->tbbm->nama ?? '-') . '</span></div>';
                                    $html .= '<div><span class="font-medium text-gray-700">TOP:</span> <span class="text-gray-900">' . ($transaksi->top_pembayaran ?? 0) . ' hari</span></div>';
                                    $html .= '</div>';

                                    $html .= '</div>';

                                    // Informasi khusus berdasarkan tipe
                                    if ($transaksi->tipe === 'jasa') {
                                        $html .= '<div class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">';
                                        $html .= '<h4 class="font-medium text-blue-900 mb-2">Informasi Transaksi Jasa</h4>';
                                        $html .= '<ul class="text-sm text-blue-800 space-y-1">';
                                        $html .= '<li>• Pilih tipe invoice: Jasa Angkut Satuan atau Tagihan Pola</li>';
                                        $html .= '<li>• Jasa Angkut Satuan: Menggunakan ongkos angkut per unit</li>';
                                        $html .= '<li>• Tagihan Pola: Input manual nama item dan total</li>';
                                        $html .= '</ul>';
                                        $html .= '</div>';
                                    } else {
                                        $html .= '<div class="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">';
                                        $html .= '<h4 class="font-medium text-green-900 mb-2">Informasi Transaksi Dagang</h4>';
                                        $html .= '<ul class="text-sm text-green-800 space-y-1">';
                                        $html .= '<li>• Harga satuan dari data penjualan</li>';
                                        $html .= '<li>• Dapat menambahkan biaya operasional dan PBBKB</li>';
                                        $html .= '<li>• PPN dapat diterapkan per item</li>';
                                        $html .= '</ul>';
                                        $html .= '</div>';
                                    }

                                    $html .= '</div>';

                                    return new \Illuminate\Support\HtmlString($html);
                                } catch (\Exception $e) {
                                    return 'Error loading transaction info: ' . $e->getMessage();
                                }
                            })
                            ->columnSpanFull(),

                        // Tipe Invoice untuk Transaksi Jasa
                        Forms\Components\Select::make('tipe_invoice_jasa')
                            ->label('Tipe Invoice Jasa')
                            ->options([
                                'jasa_angkut_satuan' => 'Jasa Angkut Satuan',
                                'tagihan_pola' => 'Tagihan Pola',
                            ])
                            ->default('jasa_angkut_satuan')
                            ->live()
                            ->visible(function (callable $get) {
                                $transaksiId = $get('id_transaksi');
                                if ($transaksiId) {
                                    $transaksi = \App\Models\TransaksiPenjualan::find($transaksiId);
                                    return $transaksi && $transaksi->tipe === 'jasa';
                                }
                                return false;
                            })
                            ->helperText('Pilih tipe invoice untuk transaksi jasa')
                            ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                // Reset invoice items when changing type
                                $set('invoice_items', []);

                                // If switching to tagihan pola, set default values for new items
                                if ($state === 'tagihan_pola') {
                                    $set('invoice_items', [[
                                        'item_id' => 8, // Default tagihan pola item
                                        'item_name' => '',
                                        'item_description' => '',
                                        'notes' => '',
                                        'quantity' => 1,
                                        'unit' => 'Paket',
                                        'unit_price' => 0,
                                        'subtotal' => 0,
                                        'include_ppn' => false,
                                        'ppn_rate' => 11,
                                        'ppn_amount' => 0,
                                        'total_amount' => 0,
                                        'total_ongkos_angkut' => 0,
                                        'operasional_amount' => 0,
                                        'pbbkb_amount' => 0,
                                    ]]);
                                }
                            }),
                    ])
                    ->visible(fn(callable $get) => !$get('is_standalone'))
                    ->columns(1),

                Forms\Components\Section::make('Informasi Pelanggan')
                    ->schema([
                        Forms\Components\TextInput::make('nama_pelanggan')
                            ->label('Nama Pelanggan')
                            ->required()
                            ->dehydrated()
                            ->live()
                            ->helperText('Otomatis terisi dari transaksi penjualan'),

                        Forms\Components\Textarea::make('alamat_pelanggan')
                            ->label('Alamat Pelanggan')

                            ->dehydrated()
                            ->live()
                            ->rows(3)
                            ->columnSpanFull()
                            ->helperText('Otomatis terisi dari transaksi penjualan'),

                        Forms\Components\TextInput::make('npwp_pelanggan')
                            ->label('NPWP Pelanggan')
                            ->dehydrated()
                            ->live()
                            ->helperText('Otomatis terisi dari transaksi penjualan'),


                    ])->columns(2),

                Forms\Components\Section::make('Detail Item Transaksi')
                    ->schema([
                        Forms\Components\Placeholder::make('item_details_info')
                            ->label('')
                            ->live()
                            ->content(function (callable $get) {
                                $transaksiId = $get('id_transaksi');
                                if (!$transaksiId) {
                                    return 'Pilih Transaksi Penjualan untuk melihat detail item';
                                }

                                try {
                                    $transaksi = \App\Models\TransaksiPenjualan::with([
                                        'penjualanDetails.item'
                                    ])->find($transaksiId);

                                    if (!$transaksi) {
                                        return 'Data transaksi tidak ditemukan';
                                    }

                                    $details = $transaksi->penjualanDetails;
                                    if ($details->isEmpty()) {
                                        return 'Tidak ada detail item dalam transaksi';
                                    }

                                    $html = '<div class="space-y-2">';
                                    $totalSubtotal = 0;
                                    $totalVolume = 0;

                                    foreach ($details as $detail) {
                                        $itemName = $detail->item->name ?? 'Unknown Item';
                                        $volume = number_format($detail->volume_item, 0, ',', '.');
                                        $harga = number_format($detail->harga_jual, 0, ',', '.');
                                        $subtotal = $detail->volume_item * $detail->harga_jual;
                                        $subtotalFormatted = number_format($subtotal, 0, ',', '.');

                                        $totalSubtotal += $subtotal;
                                        $totalVolume += $detail->volume_item;

                                        $html .= '<div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">';
                                        $html .= '<div>';
                                        $html .= '<div class="font-medium text-gray-900">' . $itemName . '</div>';
                                        $html .= '<div class="text-sm text-gray-600">' . $volume . ' liter × Rp ' . $harga . '</div>';
                                        $html .= '</div>';
                                        $html .= '<div class="text-right">';
                                        $html .= '<div class="font-medium text-gray-900">Rp ' . $subtotalFormatted . '</div>';
                                        $html .= '</div>';
                                        $html .= '</div>';
                                    }

                                    $html .= '<div class="border-t pt-2 mt-2">';
                                    $html .= '<div class="flex justify-between items-center font-bold text-lg">';
                                    $html .= '<span>Total (' . number_format($totalVolume, 0, ',', '.') . ' liter)</span>';
                                    $html .= '<span>Rp ' . number_format($totalSubtotal, 0, ',', '.') . '</span>';
                                    $html .= '</div>';
                                    $html .= '</div>';
                                    $html .= '</div>';

                                    return new \Illuminate\Support\HtmlString($html);
                                } catch (\Exception $e) {
                                    return 'Error loading item details: ' . $e->getMessage();
                                }
                            })
                            ->columnSpanFull(),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('Detail Item & Perhitungan Biaya')
                    ->schema([
                        Forms\Components\Repeater::make('invoiceItems')
                            ->label('Item Invoice')
                            ->relationship('invoiceItems')
                            ->schema([
                                Forms\Components\Grid::make(4)
                                    ->schema([
                                        Forms\Components\Select::make('item_id')
                                            ->label('Item')
                                            ->relationship('item', 'name')
                                            ->searchable()
                                            ->preload()
                                            ->required(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $transaksiId = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($isStandalone) {
                                                    return false;
                                                }

                                                if ($transaksiId) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($transaksiId);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return false; // Not required for tagihan pola
                                                    }
                                                }

                                                return true;
                                            })
                                            ->reactive()
                                            ->visible(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $transaksiId = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($isStandalone) {
                                                    return false;
                                                }

                                                if ($transaksiId) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($transaksiId);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return false;
                                                    }
                                                }

                                                return true;
                                            })
                                            ->afterStateUpdated(function (callable $set, callable $get, $state) {
                                                if ($state) {
                                                    $item = \App\Models\Item::find($state);
                                                    if ($item) {
                                                        $set('item_name', $item->name);
                                                        $set('item_description', $item->description);
                                                        $set('unit', $item->satuan->nama ?? 'Liter');
                                                    }
                                                } else {
                                                    // For tagihan pola, set default values
                                                    $transaksiId = $get('../../id_transaksi');
                                                    $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                    if ($transaksiId) {
                                                        $transaksi = \App\Models\TransaksiPenjualan::find($transaksiId);
                                                        if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                            $set('item_id', 0); // Default item_id for tagihan pola
                                                            $set('quantity', 1); // Default quantity
                                                            $set('unit', 'Paket'); // Default unit
                                                        }
                                                    }
                                                }
                                            }),

                                        Forms\Components\TextInput::make('item_name')
                                            ->label('Nama Item')
                                            ->disabled(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $transaksiId = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($isStandalone) {
                                                    return false; // Editable for standalone
                                                }

                                                if ($transaksiId) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($transaksiId);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return false; // Editable for tagihan pola
                                                    }
                                                }

                                                return true; // Disabled for normal transaction items
                                            })
                                            ->required(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $transaksiId = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($isStandalone) {
                                                    return true;
                                                }

                                                if ($transaksiId) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($transaksiId);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return true;
                                                    }
                                                }

                                                return false;
                                            })
                                            ->dehydrated()
                                            ->placeholder(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $transaksiId = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($isStandalone) {
                                                    return 'Masukkan nama item';
                                                }

                                                if ($transaksiId) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($transaksiId);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return 'Masukkan nama item untuk tagihan pola';
                                                    }
                                                }

                                                return 'Otomatis terisi dari item yang dipilih';
                                            })
                                            ->reactive()
                                            ->afterStateUpdated(function (callable $set, callable $get) {
                                                static::calculateItemTotals($set, $get);
                                            }),

                                        Forms\Components\TextInput::make('quantity')
                                            ->label('Volume/Quantity')
                                            ->numeric()
                                            ->required()
                                            ->minValue(0.01)
                                            ->default(function (callable $get) {
                                                $transaksiId = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($transaksiId) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($transaksiId);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return 1; // Default for tagihan pola
                                                    }
                                                }
                                                return 1;
                                            })
                                            ->disabled(function (callable $get) {
                                                $transaksiId = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($transaksiId) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($transaksiId);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return true; // Disabled for tagihan pola
                                                    }
                                                }
                                                return false;
                                            })
                                            ->reactive()
                                            ->afterStateUpdated(function (callable $set, callable $get) {
                                                static::calculateItemTotals($set, $get);
                                            }),

                                        Forms\Components\TextInput::make('unit')
                                            ->label('Satuan')
                                            ->default(function (callable $get) {
                                                $transaksiId = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($transaksiId) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($transaksiId);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return 'Paket'; // Default for tagihan pola
                                                    }
                                                }
                                                return 'Liter';
                                            })
                                            ->required()
                                            ->disabled(function (callable $get) {
                                                $transaksiId = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($transaksiId) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($transaksiId);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return true; // Disabled for tagihan pola
                                                    }
                                                }
                                                return false;
                                            }),
                                    ]),

                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('unit_price')
                                            ->label('Harga Satuan')
                                            ->numeric()
                                            ->prefix(function (callable $get) {
                                                $letterSettingId = $get('../../letter_setting_id');
                                                if ($letterSettingId) {
                                                    $letterSetting = \App\Models\LetterSetting::find($letterSettingId);
                                                    return \App\Services\CurrencyService::getCurrencySymbol($letterSetting?->locale ?? 'id');
                                                }
                                                return 'Rp';
                                            })
                                            ->required(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $idTransaksi = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($isStandalone) {
                                                    return true;
                                                }

                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    if ($transaksi && $transaksi->tipe === 'jasa') {
                                                        // For tagihan pola, unit price is not used (only total)
                                                        return $tipeInvoiceJasa !== 'tagihan_pola';
                                                    }
                                                    return true; // Required for dagang
                                                }
                                                return true;
                                            })
                                            ->disabled(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $idTransaksi = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($isStandalone) {
                                                    return false; // Editable for standalone
                                                }

                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    if ($transaksi && $transaksi->tipe === 'jasa') {
                                                        if ($tipeInvoiceJasa === 'tagihan_pola') {
                                                            return true; // Disabled for tagihan pola
                                                        }
                                                        return true; // Disabled for jasa angkut satuan (auto-filled from ongkos angkut)
                                                    }
                                                }
                                                return false;
                                            })
                                            ->visible(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $idTransaksi = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($isStandalone) {
                                                    return true;
                                                }

                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return false; // Hidden for tagihan pola
                                                    }
                                                }
                                                return true;
                                            })
                                            ->helperText(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $idTransaksi = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($isStandalone) {
                                                    return 'Masukkan harga satuan item';
                                                }

                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    if ($transaksi && $transaksi->tipe === 'jasa') {
                                                        if ($tipeInvoiceJasa === 'jasa_angkut_satuan') {
                                                            return 'Untuk Jasa Angkut Satuan, harga satuan diambil dari biaya ongkos angkut';
                                                        }
                                                    }
                                                }
                                                return null;
                                            })
                                            ->reactive()
                                            ->afterStateUpdated(function (callable $set, callable $get) {
                                                static::calculateItemTotals($set, $get);
                                            }),

                                        Forms\Components\TextInput::make('total_amount')
                                            ->label(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $idTransaksi = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($isStandalone) {
                                                    return 'Total';
                                                }

                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return 'Total (Input Manual)';
                                                    }
                                                }
                                                return 'Total (Otomatis)';
                                            })
                                            ->numeric()
                                            ->prefix('Rp')
                                            ->disabled(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $idTransaksi = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($isStandalone) {
                                                    return true; // Auto-calculated for standalone
                                                }

                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return false; // Editable for tagihan pola
                                                    }
                                                }
                                                return true; // Auto-calculated for others
                                            })
                                            ->required(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $idTransaksi = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return true; // Required for tagihan pola
                                                    }
                                                }
                                                return false;
                                            })
                                            ->helperText(function (callable $get) {
                                                $isStandalone = $get('../../is_standalone');
                                                $idTransaksi = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        return 'Masukkan total amount secara manual untuk tagihan pola';
                                                    }
                                                }
                                                return 'Total otomatis dihitung dari quantity × unit price';
                                            })
                                            ->reactive()
                                            ->afterStateUpdated(function (callable $set, callable $get) {
                                                // For tagihan pola, when total is manually entered, calculate PPN if needed
                                                $isStandalone = $get('../../is_standalone');
                                                $idTransaksi = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                        $totalAmount = (float) ($get('total_amount') ?? 0);
                                                        $includePpn = $get('include_ppn') ?? false;
                                                        $ppnRate = (float) ($get('ppn_rate') ?? 11);

                                                        if ($includePpn) {
                                                            $ppnAmount = $totalAmount * $ppnRate / 100;
                                                            $set('ppn_amount', $ppnAmount);
                                                        } else {
                                                            $set('ppn_amount', 0);
                                                        }

                                                        // Set subtotal as total minus PPN
                                                        $subtotal = $includePpn ? $totalAmount : $totalAmount;
                                                        $set('subtotal', $subtotal);
                                                        return;
                                                    }
                                                }

                                                // For other types, use normal calculation
                                                static::calculateItemTotals($set, $get);
                                            })
                                            ->dehydrated(),
                                    ]),

                                Forms\Components\Fieldset::make('Pajak & Biaya Tambahan')
                                    ->schema([
                                        // Ongkos Angkut - khusus untuk tipe jasa
                                        Forms\Components\Grid::make(2)
                                            ->schema([
                                                Forms\Components\TextInput::make('ongkos_angkut')
                                                    ->label('Ongkos Angkut per Unit')
                                                    ->numeric()
                                                    ->prefix('Rp')
                                                    ->default(0)
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        static::calculateItemTotals($set, $get);
                                                    })
                                                    ->helperText('Biaya ongkos angkut per unit untuk transaksi jasa'),

                                                Forms\Components\TextInput::make('total_ongkos_angkut')
                                                    ->label('Total Ongkos Angkut')
                                                    ->numeric()
                                                    ->prefix('Rp')
                                                    ->disabled()
                                                    ->dehydrated()
                                                    ->helperText('Otomatis dihitung: Ongkos Angkut × Quantity'),
                                            ])
                                            ->visible(function (callable $get) {
                                                // Show only for service type SO with jasa_angkut_satuan
                                                $idTransaksi = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    return $transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'jasa_angkut_satuan';
                                                }
                                                return false;
                                            }),

                                        // PPN Section for Tagihan Pola
                                        Forms\Components\Grid::make(3)
                                            ->schema([
                                                Forms\Components\Toggle::make('include_ppn')
                                                    ->label('Gunakan PPN')
                                                    ->default(false)
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        // For tagihan pola, recalculate PPN based on total amount
                                                        $idTransaksi = $get('../../id_transaksi');
                                                        $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                        if ($idTransaksi) {
                                                            $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                            if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                                $totalAmount = (float) ($get('total_amount') ?? 0);
                                                                $includePpn = $get('include_ppn') ?? false;
                                                                $ppnRate = (float) ($get('ppn_rate') ?? 11);

                                                                if ($includePpn && $totalAmount > 0) {
                                                                    $ppnAmount = $totalAmount * $ppnRate / 100;
                                                                    $set('ppn_amount', $ppnAmount);
                                                                } else {
                                                                    $set('ppn_amount', 0);
                                                                }
                                                                return;
                                                            }
                                                        }

                                                        // For other types, use normal calculation
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('ppn_rate')
                                                    ->label('Tarif PPN (%)')
                                                    ->numeric()
                                                    ->default(11)
                                                    ->suffix('%')
                                                    ->visible(fn(callable $get) => $get('include_ppn'))
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        // For tagihan pola, recalculate PPN based on total amount
                                                        $idTransaksi = $get('../../id_transaksi');
                                                        $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                        if ($idTransaksi) {
                                                            $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                            if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                                $totalAmount = (float) ($get('total_amount') ?? 0);
                                                                $includePpn = $get('include_ppn') ?? false;
                                                                $ppnRate = (float) ($get('ppn_rate') ?? 11);

                                                                if ($includePpn && $totalAmount > 0) {
                                                                    $ppnAmount = $totalAmount * $ppnRate / 100;
                                                                    $set('ppn_amount', $ppnAmount);
                                                                } else {
                                                                    $set('ppn_amount', 0);
                                                                }
                                                                return;
                                                            }
                                                        }

                                                        // For other types, use normal calculation
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('ppn_amount')
                                                    ->label('Jumlah PPN')
                                                    ->numeric()
                                                    ->prefix('Rp')
                                                    ->disabled()
                                                    ->dehydrated()
                                                    ->visible(fn(callable $get) => $get('include_ppn')),
                                            ])
                                            ->visible(function (callable $get) {
                                                // Show for tagihan pola
                                                $idTransaksi = $get('../../id_transaksi');
                                                $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    return $transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola';
                                                }
                                                return false;
                                            }),

                                        Forms\Components\Grid::make(3)
                                            ->schema([
                                                Forms\Components\Toggle::make('include_ppn')
                                                    ->label('PPN')
                                                    ->default(false)
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        static::calculateItemTotals($set, $get);
                                                    })
                                                    ->visible(function (callable $get) {
                                                        // Hide for tagihan pola (has its own PPN section above)
                                                        $idTransaksi = $get('../../id_transaksi');
                                                        $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                        if ($idTransaksi) {
                                                            $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                            if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                                return false;
                                                            }
                                                        }
                                                        return true;
                                                    }),

                                                Forms\Components\TextInput::make('ppn_rate')
                                                    ->label('Tarif PPN (%)')
                                                    ->numeric()
                                                    ->default(11)
                                                    ->suffix('%')
                                                    ->visible(function (callable $get) {
                                                        $includePpn = $get('include_ppn');
                                                        $idTransaksi = $get('../../id_transaksi');
                                                        $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                        if (!$includePpn) {
                                                            return false;
                                                        }

                                                        // Hide for tagihan pola (has its own PPN section above)
                                                        if ($idTransaksi) {
                                                            $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                            if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                                return false;
                                                            }
                                                        }
                                                        return true;
                                                    })
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('ppn_amount')
                                                    ->label('Jumlah PPN')
                                                    ->numeric()
                                                    ->prefix('Rp')
                                                    ->disabled()
                                                    ->dehydrated()
                                                    ->visible(function (callable $get) {
                                                        $includePpn = $get('include_ppn');
                                                        $idTransaksi = $get('../../id_transaksi');
                                                        $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

                                                        if (!$includePpn) {
                                                            return false;
                                                        }

                                                        // Hide for tagihan pola (has its own PPN section above)
                                                        if ($idTransaksi) {
                                                            $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                            if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                                                                return false;
                                                            }
                                                        }
                                                        return true;
                                                    }),
                                            ]),

                                        Forms\Components\Grid::make(3)
                                            ->schema([
                                                Forms\Components\Toggle::make('include_operasional')
                                                    ->label('Operasional')
                                                    ->default(false)
                                                    ->reactive()
                                                    ->visible(function (callable $get) {
                                                        // Hide for service type SO
                                                        $idTransaksi = $get('../../../id_transaksi');
                                                        if ($idTransaksi) {
                                                            $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                            return !($transaksi && $transaksi->tipe === 'jasa');
                                                        }
                                                        return true;
                                                    })
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('operasional_rate')
                                                    ->label('Tarif per Liter')
                                                    ->numeric()
                                                    ->default(968)
                                                    ->prefix('Rp')
                                                    ->visible(fn(callable $get) => $get('include_operasional'))
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('operasional_amount')
                                                    ->label('Jumlah Operasional')
                                                    ->numeric()
                                                    ->prefix('Rp')
                                                    ->disabled()
                                                    ->dehydrated()
                                                    ->visible(fn(callable $get) => $get('include_operasional')),
                                            ])
                                            ->visible(function (callable $get) {
                                                // Hide entire grid for service type SO
                                                $idTransaksi = $get('../../id_transaksi');
                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    return !($transaksi && $transaksi->tipe === 'jasa');
                                                }
                                                return true;
                                            }),

                                        Forms\Components\Grid::make(3)
                                            ->schema([
                                                Forms\Components\Toggle::make('include_pbbkb')
                                                    ->label('PBBKB')
                                                    ->default(false)
                                                    ->reactive()
                                                    ->visible(function (callable $get) {
                                                        // Hide for service type SO
                                                        $idTransaksi = $get('../../../id_transaksi');
                                                        if ($idTransaksi) {
                                                            $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                            return !($transaksi && $transaksi->tipe === 'jasa');
                                                        }
                                                        return true;
                                                    })
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        // Ensure pbbkb_rate has a default value when toggled
                                                        if ($get('include_pbbkb') && !$get('pbbkb_rate')) {
                                                            $set('pbbkb_rate', 0);
                                                        }
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('pbbkb_rate')
                                                    ->label('Tarif per Liter')
                                                    ->numeric()
                                                    ->default(0)
                                                    ->prefix('Rp')
                                                    ->visible(fn(callable $get) => $get('include_pbbkb'))
                                                    ->reactive()
                                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                                        static::calculateItemTotals($set, $get);
                                                    }),

                                                Forms\Components\TextInput::make('pbbkb_amount')
                                                    ->label('Jumlah PBBKB')
                                                    ->numeric()
                                                    ->prefix('Rp')
                                                    ->disabled()
                                                    ->dehydrated()
                                                    ->visible(fn(callable $get) => $get('include_pbbkb')),
                                            ])
                                            ->visible(function (callable $get) {
                                                // Hide entire grid for service type SO
                                                $idTransaksi = $get('../../id_transaksi');
                                                if ($idTransaksi) {
                                                    $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                                    return !($transaksi && $transaksi->tipe === 'jasa');
                                                }
                                                return true;
                                            }),
                                    ]),

                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\Textarea::make('item_description')
                                            ->label('Deskripsi Item')
                                            ->rows(2)
                                            ->dehydrated()
                                            ->placeholder('Otomatis terisi dari item, dapat diedit manual')
                                            ->helperText('Deskripsi akan terisi otomatis dari item, namun dapat diedit sesuai kebutuhan'),

                                        Forms\Components\Textarea::make('notes')
                                            ->label('Catatan')
                                            ->rows(2)
                                            ->placeholder('Catatan tambahan untuk item ini'),
                                    ]),
                            ])
                            ->addActionLabel('Tambah Item')
                            ->reorderable()
                            ->collapsible()
                            ->itemLabel(
                                fn(array $state): ?string =>
                                isset($state['item_name']) && isset($state['quantity'])
                                    ? $state['item_name'] . ' - ' . $state['quantity'] . ' ' . ($state['unit'] ?? 'Unit')
                                    : 'Item Baru'
                            )
                            ->columnSpanFull()
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, callable $get) {
                                static::updateInvoiceTotals($set, $get);
                            }),

                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('load_from_transaction')
                                ->label('Muat dari Transaksi Penjualan')
                                ->icon('heroicon-o-arrow-path')
                                ->color('info')
                                ->action(function (callable $set, callable $get) {
                                    $idTransaksi = $get('id_transaksi');
                                    if (!$idTransaksi) return;

                                    $transaksi = \App\Models\TransaksiPenjualan::with('penjualanDetails.item.satuan')->find($idTransaksi);
                                    if (!$transaksi) return;

                                    $items = [];
                                    $isServiceType = $transaksi->tipe === 'jasa';

                                    foreach ($transaksi->penjualanDetails as $detail) {
                                        if ($detail->item) {
                                            if ($isServiceType) {
                                                // For service type: include ongkos angkut field
                                                $items[] = [
                                                    'item_id' => $detail->id_item,
                                                    'item_name' => $detail->item->name,
                                                    'item_description' => $detail->item->description,
                                                    'quantity' => $detail->volume_item ?? $detail->volume_item,
                                                    'unit' => $detail->item->satuan->nama ?? 'Unit',
                                                    'unit_price' => 0, // Will be set from ongkos angkut
                                                    'total_amount' => 0,
                                                    'ongkos_angkut' => 0, // Default value, user can input
                                                    'total_ongkos_angkut' => 0,
                                                    'include_ppn' => true,
                                                    'ppn_amount' => 0,
                                                    'ppn_rate' => 11,
                                                    'include_operasional' => false,
                                                    'include_pbbkb' => false,
                                                    'notes' => 'Dimuat dari SO Jasa - masukkan ongkos angkut per unit',
                                                ];
                                            } else {
                                                // For trade type: normal pricing
                                                $items[] = [
                                                    'item_id' => $detail->id_item,
                                                    'item_name' => $detail->item->name,
                                                    'item_description' => $detail->item->description,
                                                    'quantity' => $detail->volume_item ?? $detail->volume_item,
                                                    'unit' => $detail->item->satuan->nama ?? 'Liter',
                                                    'unit_price' => $detail->harga_jual,
                                                    // total price
                                                    'total_amount' => $detail->harga_jual * ($detail->volume_item ?? $detail->volume_item),
                                                    'include_ppn' => true,
                                                    'ppn_amount' => 0,
                                                    'ppn_rate' => 11,
                                                    'include_operasional' => true, // User can enable manually
                                                    'operasional_amount' => 0,
                                                    // Default 968, user can change
                                                    'operasional_rate' => 968,
                                                    'include_pbbkb' => true, // User can enable manually
                                                    'pbbkb_amount' => 0,
                                                    'pbbkb_rate' => 0,
                                                    'notes' => 'Dimuat dari transaksi penjualan',
                                                ];
                                            }
                                        }
                                    }

                                    $set('invoiceItems', $items);
                                })
                                ->visible(fn(callable $get) => !empty($get('id_transaksi'))),
                        ])
                            ->columnSpanFull(),

                        // Recalculate Button
                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('recalculate')
                                ->label('🔄 Recalculate Totals')
                                ->color('primary')
                                ->size('sm')
                                ->action(function (callable $set, callable $get) {
                                    // Get all invoice items
                                    $invoiceItems = $get('invoice_items') ?? [];

                                    $subtotal = 0;
                                    $totalPpn = 0;
                                    $totalOperasional = 0;
                                    $totalPbbkb = 0;

                                    foreach ($invoiceItems as $item) {
                                        $itemSubtotal = (float) ($item['subtotal'] ?? 0);
                                        $itemPpn = (float) ($item['ppn_amount'] ?? 0);
                                        $itemOperasional = (float) ($item['operasional_amount'] ?? 0);
                                        $itemPbbkb = (float) ($item['pbbkb_amount'] ?? 0);

                                        $subtotal += $itemSubtotal;
                                        $totalPpn += $itemPpn;
                                        $totalOperasional += $itemOperasional;
                                        $totalPbbkb += $itemPbbkb;
                                    }

                                    // Set calculated values
                                    $set('subtotal', $subtotal);
                                    $set('total_pajak', $totalPpn);
                                    $set('biaya_operasional_kerja', $totalOperasional);
                                    $set('biaya_pbbkb', $totalPbbkb);

                                    // Calculate final total
                                    $totalInvoice = $subtotal + $totalPpn + $totalOperasional + $totalPbbkb;
                                    $set('total_invoice', $totalInvoice);
                                    $set('sisa_tagihan', $totalInvoice);
                                })
                                ->tooltip('Klik untuk menghitung ulang semua total berdasarkan item invoice'),
                        ])
                            ->columnSpanFull(),

                        // Perhitungan Total
                        Forms\Components\Grid::make(2)
                            ->schema([
                                // Base amount from invoice items
                                Forms\Components\TextInput::make('subtotal')
                                    ->label('Subtotal (dari Item Invoice)')
                                    ->numeric()
                                    ->prefix(function (callable $get) {
                                        $letterSettingId = $get('letter_setting_id');
                                        if ($letterSettingId) {
                                            $letterSetting = \App\Models\LetterSetting::find($letterSettingId);
                                            return \App\Services\CurrencyService::getCurrencySymbol($letterSetting?->locale ?? 'id');
                                        }
                                        return 'Rp';
                                    })
                                    ->disabled()
                                    ->dehydrated()
                                    ->live()
                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                        static::updateTotals($set, $get);
                                    })
                                    ->helperText('Otomatis dihitung dari total semua item invoice. Saat edit, gunakan tombol "Hitung Ulang Total" jika perlu recalculate.'),

                                // Transport cost (only for service type SO)
                                Forms\Components\TextInput::make('biaya_ongkos_angkut')
                                    ->label('Biaya Ongkos Angkut')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->default(0)
                                    ->reactive()
                                    ->visible(function (callable $get) {
                                        $idTransaksi = $get('id_transaksi');
                                        if (!$idTransaksi) return false;

                                        $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                        return $transaksi && $transaksi->tipe === 'jasa';
                                    })
                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                        static::updateTotals($set, $get);
                                        // Update invoice items for service type
                                        static::updateServiceInvoiceItems($set, $get);
                                    })
                                    ->helperText('Khusus untuk SO tipe Jasa - akan dikalikan ke setiap item'),

                                // Operational costs (calculated from items) - only for non-service SO
                                Forms\Components\TextInput::make('biaya_operasional_kerja')
                                    ->label('Total Biaya Operasional (dari Item Invoice)')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->disabled()
                                    ->dehydrated()
                                    ->live()
                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                        static::updateTotals($set, $get);
                                    })
                                    ->visible(function (callable $get) {
                                        $idTransaksi = $get('id_transaksi');
                                        if (!$idTransaksi) return true;

                                        $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                        return !($transaksi && $transaksi->tipe === 'jasa');
                                    })
                                    ->helperText('Otomatis dihitung dari total operasional semua item invoice'),

                                // Hidden field to maintain compatibility
                                Forms\Components\Hidden::make('include_operasional_kerja')
                                    ->default(true)
                                    ->dehydrated(),

                                // PPN from invoice items
                                Forms\Components\Placeholder::make('ppn_info')
                                    ->label('Informasi PPN')
                                    // default 0
                                    ->default(0)
                                    ->content('PPN dihitung per item di bagian Detail Item Invoice. Total PPN akan otomatis terakumulasi di sini.')
                                    ->columnSpanFull(),

                                Forms\Components\TextInput::make('total_pajak')
                                    ->label('Total PPN (dari Item Invoice)')
                                    ->numeric()
                                    ->prefix(function (callable $get) {
                                        $letterSettingId = $get('letter_setting_id');
                                        if ($letterSettingId) {
                                            $letterSetting = \App\Models\LetterSetting::find($letterSettingId);
                                            return \App\Services\CurrencyService::getCurrencySymbol($letterSetting?->locale ?? 'id');
                                        }
                                        return 'Rp';
                                    })
                                    ->disabled()
                                    ->default(0)
                                    ->dehydrated()
                                    ->live()
                                    ->afterStateUpdated(function (callable $set, callable $get) {
                                        static::updateTotals($set, $get);
                                    })
                                    ->helperText('Otomatis dihitung dari total PPN semua item invoice'),

                                // Hidden field to maintain compatibility
                                Forms\Components\Hidden::make('include_ppn')
                                    ->default(true)
                                    ->dehydrated(),

                                // PBBKB costs (calculated from items) - only for non-service SO
                                Forms\Components\TextInput::make('biaya_pbbkb')
                                    ->label('Total Biaya PBBKB (dari Item Invoice)')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->disabled()
                                    ->dehydrated()
                                    ->visible(function (callable $get) {
                                        $idTransaksi = $get('id_transaksi');
                                        if (!$idTransaksi) return true;

                                        $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                                        return !($transaksi && $transaksi->tipe === 'jasa');
                                    })
                                    ->helperText('Otomatis dihitung dari total PBBKB semua item invoice'),

                                // Hidden field to maintain compatibility
                                Forms\Components\Hidden::make('include_pbbkb')
                                    ->default(true)
                                    ->dehydrated(),


                            ])
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Informasi Tambahan')
                    ->schema([
                        Forms\Components\Textarea::make('catatan')
                            ->label('Catatan')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(1),






            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nomor_invoice')
                    ->label('Nomor Invoice')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('transaksiPenjualan.nomor_transaksi')
                    ->label('Nomor Transaksi')
                    ->formatStateUsing(function ($state, $record) {
                        if ($record->is_standalone || !$record->transaksiPenjualan) {
                            return 'Standalone Invoice';
                        }
                        return $state;
                    })
                    ->description(function ($record) {
                        if ($record->is_standalone || !$record->transaksiPenjualan) {
                            return 'Invoice mandiri tanpa transaksi';
                        }
                        $tipe = $record->transaksiPenjualan->tipe;
                        return 'SO: ' . ($record->transaksiPenjualan->kode ?? '-') . ' | ' . ucfirst($tipe);
                    })
                    ->searchable(query: function ($query, $search) {
                        return $query->whereHas('transaksiPenjualan', function ($q) use ($search) {
                            $q->where('id', 'like', "%{$search}%")
                                ->orWhere('kode', 'like', "%{$search}%");
                        });
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('transaksiPenjualan.tipe')
                    ->label('Tipe Transaksi')
                    ->badge()
                    ->color(fn(?string $state): string => match ($state) {
                        'jasa' => 'info',
                        'dagang' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(?string $state): string => match ($state) {
                        'jasa' => 'Jasa',
                        'dagang' => 'Dagang',
                        default => 'Standalone',
                    })
                    ->getStateUsing(function ($record) {
                        // Handle standalone invoices
                        if ($record->is_standalone || !$record->transaksiPenjualan) {
                            return null;
                        }
                        return $record->transaksiPenjualan->tipe;
                    })
                    ->searchable(query: function ($query, $search) {
                        return $query->whereHas('transaksiPenjualan', function ($q) use ($search) {
                            $q->where('tipe', 'like', "%{$search}%");
                        });
                    })
                    ->sortable(query: function ($query, $direction) {
                        return $query->leftJoin('transaksi_penjualan', 'invoices.id_transaksi', '=', 'transaksi_penjualan.id')
                            ->orderBy('transaksi_penjualan.tipe', $direction);
                    }),

                Tables\Columns\TextColumn::make('tipe_invoice_jasa')
                    ->label('Tipe Invoice Jasa')
                    ->badge()
                    ->color(fn(?string $state): string => match ($state) {
                        'jasa_angkut_satuan' => 'blue',
                        'tagihan_pola' => 'purple',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(?string $state): string => match ($state) {
                        'jasa_angkut_satuan' => 'Jasa Angkut Satuan',
                        'tagihan_pola' => 'Tagihan Pola',
                        default => '-',
                    })
                    ->visible(function ($record) {
                        return $record && $record->transaksiPenjualan && $record->transaksiPenjualan->tipe === 'jasa';
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('delivery_orders_count')
                    ->label('Jumlah DO')
                    ->getStateUsing(function ($record) {
                        try {
                            if (!$record->transaksiPenjualan) {
                                return 0;
                            }

                            // Use query instead of relationship property to avoid eager loading issues
                            return \App\Models\DeliveryOrder::where('id_transaksi', $record->id_transaksi)->count();
                        } catch (\Exception $e) {
                            \Illuminate\Support\Facades\Log::error('Error getting delivery orders count: ' . $e->getMessage());
                            return 0;
                        }
                    })
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('nama_pelanggan')
                    ->label('Pelanggan')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('tanggal_invoice')
                    ->label('Tanggal Invoice')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('tanggal_jatuh_tempo')
                    ->label('Jatuh Tempo')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_invoice')
                    ->label('Total Invoice')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->formatCurrency($state);
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_paid')
                    ->label('Total Terbayar')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->formatCurrency($record->total_paid);
                    })
                    ->sortable(false),

                Tables\Columns\TextColumn::make('remaining_amount')
                    ->label('Sisa Tagihan')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->formatCurrency($record->remaining_amount);
                    })
                    ->sortable(false),

                Tables\Columns\TextColumn::make('payment_status')
                    ->label('Status Pembayaran')
                    ->badge()
                    ->color(fn($state) => match ($state) {
                        'unpaid' => 'danger',
                        'partial' => 'warning',
                        'paid' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(function ($state) {
                        return match ($state) {
                            'unpaid' => 'Belum Bayar',
                            'partial' => 'Sebagian',
                            'paid' => 'Lunas',
                            default => 'Unknown',
                        };
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'draft' => 'gray',
                        'sent' => 'warning',
                        'paid' => 'success',
                        'partial' => 'info',
                        'overdue' => 'danger',
                        'cancelled' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'draft' => 'Draft',
                        'sent' => 'Terkirim',
                        'paid' => 'Lunas',
                        'partial' => 'Sebagian Terbayar',
                        'overdue' => 'Jatuh Tempo',
                        'cancelled' => 'Dibatalkan',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'draft' => 'Draft',
                        'sent' => 'Terkirim',
                        'paid' => 'Lunas',
                        'partial' => 'Sebagian Terbayar',
                        'overdue' => 'Jatuh Tempo',
                        'cancelled' => 'Dibatalkan',
                    ]),

                Tables\Filters\SelectFilter::make('tipe_transaksi')
                    ->label('Tipe Transaksi')
                    ->options([
                        'jasa' => 'Jasa',
                        'dagang' => 'Dagang',
                        'standalone' => 'Standalone',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        $value = $data['value'] ?? null;

                        if ($value === 'standalone') {
                            return $query->where(function ($q) {
                                $q->where('is_standalone', true)
                                    ->orWhereNull('id_transaksi');
                            });
                        }

                        if ($value) {
                            return $query->whereHas('transaksiPenjualan', function ($q) use ($value) {
                                $q->where('tipe', $value);
                            });
                        }

                        return $query;
                    }),

                Tables\Filters\SelectFilter::make('transaksi_penjualan')
                    ->label('Transaksi Penjualan')
                    ->relationship('transaksiPenjualan', 'id')
                    ->getOptionLabelFromRecordUsing(fn($record) => $record->nomor_transaksi . ' - ' . $record->pelanggan->nama)
                    ->searchable(['id'])
                    // populate dari id_transaksi
                    ->default(function () {
                        return request()->query('id_transaksi', null);
                    })
                    ->preload(),

                Tables\Filters\Filter::make('tanggal_invoice')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_invoice', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_invoice', '<=', $date),
                            );
                    }),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PaymentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'create' => Pages\CreateInvoice::route('/create'),
            'view' => Pages\ViewInvoice::route('/{record}'),
            'edit' => Pages\EditInvoice::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with([
                'transaksiPenjualan.pelanggan',
                // 'transaksiPenjualan.deliveryOrder',
                'createdBy'
            ])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    /**
     * Calculate item totals (subtotal, PPN, operasional, PBBKB, total amount)
     */
    protected static function calculateItemTotals(callable $set, callable $get): void
    {
        // For tagihan pola, ensure all required fields have default values
        $transaksiId = $get('../../id_transaksi');
        $tipeInvoiceJasa = $get('../../tipe_invoice_jasa');

        if ($transaksiId) {
            $transaksi = \App\Models\TransaksiPenjualan::find($transaksiId);
            if ($transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola') {
                // Set all required default values for tagihan pola
                $set('item_id', 8); // Default tagihan pola item

                // Ensure quantity has default value
                if (!$get('quantity')) {
                    $set('quantity', 1);
                }

                // Ensure unit has default value
                if (!$get('unit')) {
                    $set('unit', 'Paket');
                }

                // Ensure unit_price has default value
                if (!$get('unit_price')) {
                    $set('unit_price', 0);
                }

                // Ensure PPN fields have default values
                if ($get('include_ppn') === null) {
                    $set('include_ppn', false);
                }

                if (!$get('ppn_rate')) {
                    $set('ppn_rate', 11);
                }
            }
        }

        $quantity = (float) $get('quantity') ?: 1; // Default to 1 if empty
        $unitPrice = (float) $get('unit_price') ?: 0;
        $totalAmount = (float) $get('total_amount') ?: 0;

        // Check if this is tagihan pola
        $isTagihanPola = ($transaksiId && $transaksi && $transaksi->tipe === 'jasa' && $tipeInvoiceJasa === 'tagihan_pola');

        // Check if this is a service type transaction
        $idTransaksi = $get('../../id_transaksi');
        $isServiceType = false;

        if ($idTransaksi) {
            $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
            $isServiceType = $transaksi && $transaksi->tipe === 'jasa';

            if ($isServiceType) {
                // For service type, get transport cost from item level
                $ongkosAngkutPerUnit = (float) $get('ongkos_angkut') ?: 0;

                // Calculate total ongkos angkut
                $totalOngkosAngkut = $quantity * $ongkosAngkutPerUnit;
                $set('total_ongkos_angkut', $totalOngkosAngkut);

                // Use ongkos angkut as unit price for service type
                $unitPrice = $ongkosAngkutPerUnit;
                $set('unit_price', $unitPrice);
            }
        }

        // Calculate subtotal
        if ($isTagihanPola) {
            // For tagihan pola, subtotal = total_amount (manual input)
            $subtotal = $totalAmount;
            $set('subtotal', $subtotal);
            $set('unit_price', $totalAmount); // Set unit price = total amount for consistency
        } else {
            // For normal items, subtotal = quantity * unit_price
            $subtotal = $quantity * $unitPrice;
            $set('subtotal', $subtotal);
        }

        // For service type, only calculate PPN and ongkos angkut, no operational or PBBKB
        if ($isServiceType) {
            // Calculate PPN amount
            $includePpn = $get('include_ppn') ?: false;
            $ppnRate = (float) $get('ppn_rate') ?: 11;

            if ($isTagihanPola) {
                // For tagihan pola, calculate PPN from total_amount (manual input)
                $ppnAmount = $includePpn ? ($totalAmount * $ppnRate / 100) : 0;
                $set('ppn_amount', $ppnAmount);
                // Don't recalculate total_amount for tagihan pola, it's manually set
            } else {
                // For normal service items, calculate PPN from subtotal
                $ppnAmount = $includePpn ? ($subtotal * $ppnRate / 100) : 0;
                $set('ppn_amount', $ppnAmount);
                // Calculate total amount (subtotal + PPN for service)
                $finalTotal = $subtotal + $ppnAmount;
                $set('total_amount', $finalTotal);
            }

            // Set operational and PBBKB to 0 for service type
            $set('include_operasional', false);
            $set('operasional_amount', 0);
            $set('include_pbbkb', false);
            $set('pbbkb_amount', 0);
        } else {
            // Normal calculation for non-service type
            // Calculate PPN amount
            $includePpn = $get('include_ppn') ?: false;
            $ppnRate = (float) $get('ppn_rate') ?: 11;
            $ppnAmount = $includePpn ? ($subtotal * $ppnRate / 100) : 0;
            $set('ppn_amount', $ppnAmount);

            // Calculate operational amount (per liter)
            $includeOperasional = $get('include_operasional') ?: false;
            $operasionalRate = (float) $get('operasional_rate') ?: 968;
            $operasionalAmount = $includeOperasional ? ($quantity * $operasionalRate) : 0;
            $set('operasional_amount', $operasionalAmount);

            // Calculate PBBKB amount (per liter)
            $includePbbkb = $get('include_pbbkb') ?: false;
            $pbbkbRate = (float) $get('pbbkb_rate') ?: 0;
            $pbbkbAmount = $includePbbkb ? ($quantity * $pbbkbRate) : 0;
            $set('pbbkb_amount', $pbbkbAmount);

            // Calculate total amount
            $totalAmount = $subtotal + $ppnAmount + $operasionalAmount + $pbbkbAmount;
            $set('total_amount', $totalAmount);
        }
    }

    /**
     * Update service invoice items when transport cost changes
     */
    protected static function updateServiceInvoiceItems(callable $set, callable $get): void
    {
        $biayaOngkosAngkut = (float) $get('biaya_ongkos_angkut') ?: 0;
        $invoiceItems = $get('invoiceItems') ?: [];

        // Update all invoice items with new transport cost as unit price
        foreach ($invoiceItems as $index => $item) {
            $quantity = (float) ($item['quantity'] ?? 0);
            $subtotal = $quantity * $biayaOngkosAngkut;

            // Update the item
            $set("invoiceItems.{$index}.unit_price", $biayaOngkosAngkut);
            $set("invoiceItems.{$index}.subtotal", $subtotal);

            // Recalculate PPN if included
            $includePpn = $item['include_ppn'] ?? false;
            $ppnRate = (float) ($item['ppn_rate'] ?? 11);
            $ppnAmount = $includePpn ? ($subtotal * $ppnRate / 100) : 0;
            $set("invoiceItems.{$index}.ppn_amount", $ppnAmount);

            // Set total amount (subtotal + PPN only for service)
            $totalAmount = $subtotal + $ppnAmount;
            $set("invoiceItems.{$index}.total_amount", $totalAmount);
        }

        // Update invoice totals
        static::updateInvoiceTotals($set, $get);
    }

    /**
     * Update invoice totals based on invoice items
     */
    protected static function updateInvoiceTotals(callable $set, callable $get): void
    {

        $invoiceItems = $get('invoiceItems') ?: [];

        $totalSubtotal = 0;
        $totalPpn = 0;
        $totalOperasional = 0;
        $totalPbbkb = 0;
        $totalVolume = 0;

        foreach ($invoiceItems as $item) {
            $totalSubtotal += (float) ($item['subtotal'] ?? 0);
            $totalPpn += (float) ($item['ppn_amount'] ?? 0);
            $totalOperasional += (float) ($item['operasional_amount'] ?? 0);
            $totalPbbkb += (float) ($item['pbbkb_amount'] ?? 0);
            $totalVolume += (float) ($item['quantity'] ?? 0);
        }

        $set('subtotal', $totalSubtotal);
        $set('total_pajak', $totalPpn);
        $set('biaya_operasional_kerja', $totalOperasional);
        $set('biaya_pbbkb', $totalPbbkb);

        // Update volume for reference
        $set('operasional_volume', $totalVolume);
        $set('pbbkb_volume', $totalVolume);

        // Recalculate total invoice
        static::updateTotals($set, $get);
    }

    /**
     * Calculate operational costs based on rate and volume
     */
    protected static function calculateOperasional(callable $set, callable $get): void
    {
        $rate = (float) $get('operasional_rate') ?: 0;
        $volume = (float) $get('operasional_volume') ?: 0;
        $total = $rate * $volume;

        $set('biaya_operasional_kerja', $total);
    }

    /**
     * Calculate PBBKB based on rate per 10,000L and volume
     */
    protected static function calculatePbbkb(callable $set, callable $get): void
    {
        $rate = (float) $get('pbbkb_rate') ?: 0;
        $volume = (float) $get('pbbkb_volume') ?: 0;

        // PBBKB calculation: (rate per 10,000L) * (volume / 10,000)
        $total = $rate * ($volume / 10000);

        $set('biaya_pbbkb', $total);
    }

    /**
     * Update all totals based on current values
     */
    protected static function updateTotals(callable $set, callable $get): void
    {
        try {
            // Check if this is a service type transaction
            $idTransaksi = $get('id_transaksi');
            $isServiceType = false;

            if ($idTransaksi) {
                $transaksi = \App\Models\TransaksiPenjualan::find($idTransaksi);
                $isServiceType = $transaksi && $transaksi->tipe === 'jasa';
            }

            // Get base values with proper fallbacks
            $subtotal = (float) ($get('subtotal') ?? 0);
            $totalPajak = (float) ($get('total_pajak') ?? 0);

            if ($isServiceType) {
                // For service type: subtotal + transport cost + PPN
                $biayaOngkos = (float) ($get('biaya_ongkos_angkut') ?? 0);
                $totalInvoice = $subtotal + $biayaOngkos + $totalPajak;

                \Illuminate\Support\Facades\Log::info('Service type calculation', [
                    'subtotal' => $subtotal,
                    'biaya_ongkos' => $biayaOngkos,
                    'total_pajak' => $totalPajak,
                    'total_invoice' => $totalInvoice
                ]);
            } else {
                // For trade type: include all costs
                $biayaOngkos = (float) ($get('biaya_ongkos_angkut') ?? 0);

                // Calculate operational if included
                $biayaOperasional = 0;
                if ($get('include_operasional_kerja')) {
                    $biayaOperasional = (float) ($get('biaya_operasional_kerja') ?? 0);
                }

                // Calculate PBBKB if included
                $biayaPbbkb = 0;
                if ($get('include_pbbkb')) {
                    $biayaPbbkb = (float) ($get('biaya_pbbkb') ?? 0);
                }

                // Calculate final total
                $totalInvoice = $subtotal + $biayaOngkos + $biayaOperasional + $totalPajak + $biayaPbbkb;

                \Illuminate\Support\Facades\Log::info('Trade type calculation', [
                    'subtotal' => $subtotal,
                    'biaya_ongkos' => $biayaOngkos,
                    'biaya_operasional' => $biayaOperasional,
                    'total_pajak' => $totalPajak,
                    'biaya_pbbkb' => $biayaPbbkb,
                    'total_invoice' => $totalInvoice
                ]);
            }

            $set('total_invoice', $totalInvoice);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in updateTotals: ' . $e->getMessage());
        }
    }

    /**
     * Populate form data from selected transaksi penjualan
     */
    protected static function populateFromTransaksiPenjualan(callable $set, callable $get, $idTransaksi): void
    {
        if (!$idTransaksi) return;

        try {
            // Load transaksi penjualan with all necessary relationships
            $transaksi = \App\Models\TransaksiPenjualan::with([
                'pelanggan',
                'alamatPelanggan',
                'penjualanDetails.item',
                'sph.letterSetting'
            ])->find($idTransaksi);

            if (!$transaksi) {
                \Illuminate\Support\Facades\Log::warning('Transaksi not found: ' . $idTransaksi);
                return;
            }

            $pelanggan = $transaksi->pelanggan;
            $alamatPelanggan = $transaksi->alamatPelanggan;

            \Illuminate\Support\Facades\Log::info('Populating invoice from transaksi', [
                'transaksi_id' => $idTransaksi,
                'pelanggan_nama' => $pelanggan?->nama,
                'alamat_pelanggan' => $alamatPelanggan?->alamat
            ]);

            // Auto-populate customer data
            if ($pelanggan) {
                \Illuminate\Support\Facades\Log::info('Setting customer data', [
                    'nama' => $pelanggan->nama,
                    'npwp' => $pelanggan->npwp
                ]);
                $set('nama_pelanggan', $pelanggan->nama);
                $set('npwp_pelanggan', $pelanggan->npwp);
            } else {
                \Illuminate\Support\Facades\Log::warning('No pelanggan found for transaksi: ' . $idTransaksi);
            }

            // Auto-populate customer address
            if ($alamatPelanggan) {
                $alamatLengkap = collect([
                    $alamatPelanggan->alamat,
                    $alamatPelanggan->kelurahan,
                    $alamatPelanggan->kecamatan,
                    $alamatPelanggan->kota,
                    $alamatPelanggan->provinsi,
                    $alamatPelanggan->kode_pos
                ])->filter()->implode(', ');

                \Illuminate\Support\Facades\Log::info('Setting alamat pelanggan', ['alamat' => $alamatLengkap]);
                $set('alamat_pelanggan', $alamatLengkap);
            } else {
                // Fallback to pelanggan alamat if no specific address
                $fallbackAlamat = $pelanggan->alamat ?? '';
                \Illuminate\Support\Facades\Log::info('Setting fallback alamat', ['alamat' => $fallbackAlamat]);
                $set('alamat_pelanggan', $fallbackAlamat);
            }

            // Auto-fill letter setting from SPH or default
            if ($transaksi->sph && $transaksi->sph->letterSetting) {
                // If transaksi has SPH and SPH has letter setting, use it
                $set('letter_setting_id', $transaksi->sph->letter_setting_id);
                \Illuminate\Support\Facades\Log::info('Setting letter_setting_id from SPH', [
                    'letter_setting_id' => $transaksi->sph->letter_setting_id,
                    'setting_name' => $transaksi->sph->letterSetting->name
                ]);
            } else {
                // Otherwise, use default letter setting
                $defaultLetterSetting = \App\Models\LetterSetting::where('is_default', true)->first();
                if ($defaultLetterSetting) {
                    $set('letter_setting_id', $defaultLetterSetting->id);
                    \Illuminate\Support\Facades\Log::info('Setting default letter_setting_id', [
                        'letter_setting_id' => $defaultLetterSetting->id,
                        'setting_name' => $defaultLetterSetting->name
                    ]);
                }
            }

            // Calculate subtotal from penjualan details
            $subtotal = 0;
            $totalVolume = 0;

            foreach ($transaksi->penjualanDetails as $detail) {
                $itemTotal = ($detail->volume_item ?? $detail->volume_item) * $detail->harga_jual;
                $subtotal += $itemTotal;
                $totalVolume += ($detail->volume_item ?? $detail->volume_item);
            }

            // Set calculated values
            $set('subtotal', $subtotal);

            // Set default volumes for calculations
            $set('operasional_volume', $totalVolume);
            $set('pbbkb_volume', $totalVolume);

            // Set default dates
            $set('tanggal_invoice', now());
            $set('tanggal_jatuh_tempo', now()->addDays(30));

            // Set default status
            $set('status', 'draft');

            // Trigger total calculation
            static::updateTotals($set, $get);
        } catch (\Exception $e) {
            // Log error but don't break the form
            \Illuminate\Support\Facades\Log::error('Error populating invoice from transaksi: ' . $e->getMessage());
        }
    }
}
