<?php

namespace App\Filament\Resources\UangJalanResource\Pages;

use App\Filament\Resources\UangJalanResource;
use App\Models\UangJalan;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewUangJalan extends ViewRecord
{
    protected static string $resource = UangJalanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            // lihat do
            Actions\Action::make('view_do')
                ->label('Lihat DO')
                ->icon('heroicon-o-document-text')
                ->color('primary')
                ->url(fn(UangJalan $record): string => $record->deliveryOrderUrl)
                ->visible(fn(UangJalan $record): bool => $record->deliveryOrderUrl !== null)
                ->openUrlInNewTab(false),

            // print receipt
            Actions\Action::make('print_receipt')
                ->label('Print Tanda Terima')
                ->icon('heroicon-o-printer')
                ->color('success')
                ->url(fn(UangJalan $record): string => route('uang-jalan.print-receipt', $record))
                ->openUrlInNewTab(true),
        ];
    }
}
