<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ItemResource\Pages;
use App\Filament\Resources\ItemResource\RelationManagers;
use App\Models\Item;
use App\Models\ItemKategori;
use App\Models\SatuanDasar;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;

class ItemResource extends Resource
{
    protected static ?string $model = Item::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationGroup = 'Data Master';

    protected static ?string $navigationLabel = 'Item/BBM';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Item/BBM')
                    ->schema([
                        Forms\Components\TextInput::make('kode')
                            ->label('Kode Item')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(100)
                            ->placeholder('Contoh: BBM001'),

                        Forms\Components\TextInput::make('name')
                            ->label('Nama Item')
                            ->required()
                            ->maxLength(100)
                            ->placeholder('Contoh: Solar Industri'),

                        Forms\Components\Select::make('id_item_jenis')
                            ->label('Kategori Item')
                            ->options(ItemKategori::all()->pluck('nama', 'id'))
                            ->searchable()
                            ->required()
                            ->placeholder('Pilih kategori item'),

                        Forms\Components\Select::make('id_satuan')
                            ->label('Satuan')
                            ->options(SatuanDasar::all()->pluck('nama', 'id'))
                            ->searchable()
                            ->required()
                            ->placeholder('Pilih satuan'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Deskripsi')
                    ->schema([
                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi Item')
                            ->rows(3)
                            ->placeholder('Masukkan deskripsi item')
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Media & Gambar')
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('images')
                            ->label('Gambar Produk')
                            ->collection('images')
                            ->image()
                            ->multiple()
                            ->reorderable()
                            ->maxFiles(5)
                            ->imageEditor()
                            ->imageResizeTargetWidth('800')
                            ->imageResizeTargetHeight('800')
                            ->helperText('Upload gambar produk (maksimal 5 gambar, ukuran maksimal 2MB per gambar)')
                            ->maxSize(2048)
                            ->columnSpanFull(),

                        SpatieMediaLibraryFileUpload::make('documents')
                            ->label('Dokumen Pendukung')
                            ->collection('documents')
                            ->multiple()
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                            ->maxFiles(3)
                            ->helperText('Upload dokumen pendukung seperti spesifikasi, sertifikat, dll. (PDF, JPG, PNG)')
                            ->maxSize(5120)
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                SpatieMediaLibraryImageColumn::make('images')
                    ->label('Gambar')
                    ->collection('images')
                    ->conversion('thumb')
                    ->size(50)
                    ->circular(false)
                    ->toggleable(),

                Tables\Columns\TextColumn::make('kode')
                    ->label('Kode')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('name')
                    ->label('Nama Item')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('kategori.nama')
                    ->label('Kategori')
                    ->sortable()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('satuan.nama')
                    ->label('Satuan')
                    ->sortable()
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('id_item_jenis')
                    ->label('Kategori')
                    ->options(ItemKategori::all()->pluck('nama', 'id')),

                Tables\Filters\SelectFilter::make('id_satuan')
                    ->label('Satuan')
                    ->options(SatuanDasar::all()->pluck('nama', 'id')),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListItems::route('/'),
            'create' => Pages\CreateItem::route('/create'),
            'edit' => Pages\EditItem::route('/{record}/edit'),
        ];
    }
}
