<?php

namespace App\Filament\Resources\DeliveryOrderResource\Pages;

use App\Filament\Resources\DeliveryOrderResource;
use App\Models\PenjualanDetail;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateDeliveryOrder extends CreateRecord
{
    protected static string $resource = DeliveryOrderResource::class;

    protected function afterCreate(): void
    {
        // Update volume_do in penjualan_detail based on form data
        $data = $this->data;

        if (isset($data['penjualan_details_volume']) && is_array($data['penjualan_details_volume'])) {
            foreach ($data['penjualan_details_volume'] as $item) {
                if (isset($item['id']) && isset($item['volume_do'])) {
                    PenjualanDetail::where('id', $item['id'])
                        ->update(['volume_do' => $item['volume_do']]);
                }
            }
        }
    }
}
