<?php

namespace App\Filament\Resources\DeliveryOrderResource\Pages;

use App\Filament\Resources\DeliveryOrderResource;
use App\Models\PenjualanDetail;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditDeliveryOrder extends EditRecord
{
    protected static string $resource = DeliveryOrderResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load volume data from penjualan_detail
        if (isset($data['id_transaksi'])) {
            $transaksi = \App\Models\TransaksiPenjualan::with('penjualanDetails.item')->find($data['id_transaksi']);
            if ($transaksi) {
                $items = [];
                foreach ($transaksi->penjualanDetails as $detail) {
                    $items[] = [
                        'id' => $detail->id,
                        'item_name' => $detail->item->nama_item ?? 'Unknown',
                        'volume_item' => $detail->volume_item,
                        'volume_do' => $detail->volume_do ?? 0,
                    ];
                }
                $data['penjualan_details_volume'] = $items;
            }
        }

        return $data;
    }

    protected function afterSave(): void
    {
        // Update volume_do in penjualan_detail based on form data
        $data = $this->data;

        if (isset($data['penjualan_details_volume']) && is_array($data['penjualan_details_volume'])) {
            foreach ($data['penjualan_details_volume'] as $item) {
                if (isset($item['id']) && isset($item['volume_do'])) {
                    PenjualanDetail::where('id', $item['id'])
                        ->update(['volume_do' => $item['volume_do']]);
                }
            }
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->color('info'),
            Actions\DeleteAction::make()
                ->color('danger'),
        ];
    }

    public function getTitle(): string
    {
        return 'Edit Delivery Order';
    }

    public function getSubheading(): ?string
    {
        return 'Perbarui informasi delivery order';
    }
}
