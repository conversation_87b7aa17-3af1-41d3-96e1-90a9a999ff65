<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Jabatan extends Model
{
    use SoftDeletes;

    protected $table = 'jabatan';

    protected $fillable = [
        'nama',
        'jabatan_en',
        'created_by',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function users()
    {
        return $this->hasMany(User::class, 'id_jabatan');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the position name in the specified language
     *
     * @param string $lang 'id' for Indonesian, 'en' for English
     * @return string
     */
    public function getName($lang = 'id')
    {
        return $lang === 'en' && $this->jabatan_en ? $this->jabatan_en : $this->nama;
    }

    /**
     * Get the position name in Indonesian
     *
     * @return string
     */
    public function getNameIdAttribute()
    {
        return $this->nama;
    }

    /**
     * Get the position name in English
     *
     * @return string|null
     */
    public function getNameEnAttribute()
    {
        return $this->jabatan_en;
    }

    /**
     * Get the position name with fallback to Indonesian if English is not available
     *
     * @return string
     */
    public function getDisplayNameEnAttribute()
    {
        return $this->jabatan_en ?: $this->nama;
    }

    /**
     * Scope to search by name in both languages
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearchByName($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('nama', 'like', "%{$search}%")
                ->orWhere('jabatan_en', 'like', "%{$search}%");
        });
    }

    /**
     * Get all positions with their names in both languages
     *
     * @return array
     */
    public static function getPositionOptions()
    {
        return static::all()->mapWithKeys(function ($jabatan) {
            $label = $jabatan->nama;
            if ($jabatan->jabatan_en) {
                $label .= " / {$jabatan->jabatan_en}";
            }
            return [$jabatan->id => $label];
        })->toArray();
    }
}
