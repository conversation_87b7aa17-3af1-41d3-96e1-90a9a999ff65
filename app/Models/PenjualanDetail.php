<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PenjualanDetail extends Model
{
    use SoftDeletes;

    protected $table = 'penjualan_detail';

    protected $fillable = [
        'id_transaksi_penjualan',
        'id_item',
        'volume_item',
        'volume_do',
        'harga_jual',
        'location',
        'alamat_pengiriman',
        'keterangan_lokasi',
        'created_by',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'volume_item' => 'float',
        'volume_do' => 'float',
        'harga_jual' => 'float',
        'location' => 'array',
    ];

    public function transaksiPenjualan()
    {
        return $this->belongsTo(TransaksiPenjualan::class, 'id_transaksi_penjualan');
    }

    public function item()
    {
        return $this->belongsTo(Item::class, 'id_item');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Check if detail has location coordinates
     */
    public function hasLocation(): bool
    {
        return !is_null($this->location) &&
            isset($this->location['lat']) &&
            isset($this->location['lng']);
    }

    /**
     * Get latitude from location
     */
    public function getLatitude(): ?float
    {
        return $this->hasLocation() ? (float) $this->location['lat'] : null;
    }

    /**
     * Get longitude from location
     */
    public function getLongitude(): ?float
    {
        return $this->hasLocation() ? (float) $this->location['lng'] : null;
    }

    /**
     * Get formatted coordinates string
     */
    public function getCoordinatesString(): ?string
    {
        if (!$this->hasLocation()) {
            return null;
        }

        return $this->getLatitude() . ', ' . $this->getLongitude();
    }

    /**
     * Get Google Maps URL
     */
    public function getGoogleMapsUrl(): ?string
    {
        if (!$this->hasLocation()) {
            return null;
        }

        return "https://www.google.com/maps?q=" . $this->getLatitude() . "," . $this->getLongitude();
    }
}
