<?php

namespace App\Services;

use App\Models\User;
use App\Models\Jabatan;
use App\Models\NotificationSetting;

class SignerService
{
    /**
     * Get the default signer for SPH documents
     * Priority:
     * 1. User with approved approval (if exists)
     * 2. User with "direktur" position
     * 3. User from notification settings for 'sph_manager_update_sales'
     * 4. First active user with signature
     * 
     * @param \App\Models\Sph $sph
     * @return \App\Models\User|null
     */
    public static function getDefaultSigner($sph = null): ?User
    {
        // 1. If SPH has approved approval, use that user
        if ($sph && $sph->approvedApprovals) {
            return $sph->approvedApprovals->user;
        }

        // 2. Find user with "direktur" position (case-insensitive)
        $direktur = self::findDirektur();
        if ($direktur) {
            return $direktur;
        }

        // 3. Fall back to notification settings
        $eventName = 'sph_manager_update_sales';
        $defaultApproverSetting = NotificationSetting::findActiveRecipientsForEvent($eventName)->first();
        if ($defaultApproverSetting) {
            return $defaultApproverSetting->user;
        }

        // 4. Last resort: find any active user with signature
        return User::whereNotNull('signature_path')
            ->where('is_active', true)
            ->first();
    }

    /**
     * Find user with "direktur" position
     * Searches in both Indonesian and English position names
     * 
     * @return \App\Models\User|null
     */
    public static function findDirektur(): ?User
    {
        // Find jabatan that contains "direktur" (case-insensitive)
        $direkturJabatan = Jabatan::where(function ($query) {
            $query->whereRaw('LOWER(nama) LIKE ?', ['%direktur%'])
                  ->orWhereRaw('LOWER(jabatan_en) LIKE ?', ['%director%']);
        })->first();

        if (!$direkturJabatan) {
            return null;
        }

        // Find active user with this position
        return User::where('id_jabatan', $direkturJabatan->id)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Get all users with director positions
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getAllDirektur()
    {
        $direkturJabatan = Jabatan::where(function ($query) {
            $query->whereRaw('LOWER(nama) LIKE ?', ['%direktur%'])
                  ->orWhereRaw('LOWER(jabatan_en) LIKE ?', ['%director%']);
        })->get();

        if ($direkturJabatan->isEmpty()) {
            return collect();
        }

        return User::whereIn('id_jabatan', $direkturJabatan->pluck('id'))
            ->where('is_active', true)
            ->get();
    }

    /**
     * Check if a user has director position
     * 
     * @param \App\Models\User $user
     * @return bool
     */
    public static function isDirector(User $user): bool
    {
        if (!$user->jabatan) {
            return false;
        }

        $jabatanName = strtolower($user->jabatan->nama ?? '');
        $jabatanEn = strtolower($user->jabatan->jabatan_en ?? '');

        return str_contains($jabatanName, 'direktur') || str_contains($jabatanEn, 'director');
    }
}
