<?php

namespace App\Services;

class CurrencyService
{
    /**
     * Default exchange rate IDR to USD
     * In production, this should come from a real-time API or database
     */
    const DEFAULT_EXCHANGE_RATE = 15800; // 1 USD = 15,800 IDR (approximate)

    /**
     * Convert IDR amount to USD
     *
     * @param float|int $idrAmount
     * @param float|null $exchangeRate
     * @return float
     */
    public static function idrToUsd($idrAmount, $exchangeRate = null): float
    {
        if (is_null($idrAmount) || $idrAmount == 0) {
            return 0;
        }

        $rate = $exchangeRate ?? self::DEFAULT_EXCHANGE_RATE;
        return round($idrAmount / $rate, 2);
    }

    /**
     * Convert USD amount to IDR
     *
     * @param float|int $usdAmount
     * @param float|null $exchangeRate
     * @return float
     */
    public static function usdToIdr($usdAmount, $exchangeRate = null): float
    {
        if (is_null($usdAmount) || $usdAmount == 0) {
            return 0;
        }

        $rate = $exchangeRate ?? self::DEFAULT_EXCHANGE_RATE;
        return round($usdAmount * $rate, 0);
    }

    /**
     * Format currency based on locale
     *
     * @param float|int $amount
     * @param string $locale ('id' or 'en')
     * @param bool $convertCurrency Whether to convert currency or just format
     * @return string
     */
    public static function formatCurrency($amount, string $locale = 'id', bool $convertCurrency = true): string
    {
        if (is_null($amount) || $amount == 0) {
            return $locale === 'en' ? '$0.00' : 'Rp 0';
        }

        if ($locale === 'en') {
            // Convert to USD if needed
            $usdAmount = $convertCurrency ? self::idrToUsd($amount) : $amount;
            return '$' . number_format($usdAmount, 2, '.', ',');
        } else {
            // Keep as IDR
            $idrAmount = $convertCurrency ? $amount : $amount;
            return 'Rp ' . number_format($idrAmount, 0, ',', '.');
        }
    }

    /**
     * Get currency symbol based on locale
     *
     * @param string $locale
     * @return string
     */
    public static function getCurrencySymbol(string $locale = 'id'): string
    {
        return $locale === 'en' ? '$' : 'Rp';
    }

    /**
     * Get currency code based on locale
     *
     * @param string $locale
     * @return string
     */
    public static function getCurrencyCode(string $locale = 'id'): string
    {
        return $locale === 'en' ? 'USD' : 'IDR';
    }

    /**
     * Format amount for display in templates
     *
     * @param float|int $amount
     * @param string $locale
     * @param bool $convertCurrency
     * @return array ['formatted' => string, 'amount' => float, 'currency' => string]
     */
    public static function formatForTemplate($amount, string $locale = 'id', bool $convertCurrency = true): array
    {
        if ($locale === 'en' && $convertCurrency) {
            $convertedAmount = self::idrToUsd($amount);
            return [
                'formatted' => self::formatCurrency($amount, $locale, $convertCurrency),
                'amount' => $convertedAmount,
                'currency' => 'USD',
                'symbol' => '$'
            ];
        } else {
            return [
                'formatted' => self::formatCurrency($amount, $locale, false),
                'amount' => $amount,
                'currency' => 'IDR',
                'symbol' => 'Rp'
            ];
        }
    }

    /**
     * Get current exchange rate (placeholder for future API integration)
     *
     * @return float
     */
    public static function getCurrentExchangeRate(): float
    {
        // TODO: Integrate with real-time exchange rate API
        // For now, return default rate
        return self::DEFAULT_EXCHANGE_RATE;
    }

    /**
     * Convert amount to words based on locale
     *
     * @param float|int $amount
     * @param string $locale
     * @param bool $convertCurrency
     * @return string
     */
    public static function amountToWords($amount, string $locale = 'id', bool $convertCurrency = true): string
    {
        if ($locale === 'en') {
            $usdAmount = $convertCurrency ? self::idrToUsd($amount) : $amount;
            // For English, we'll use a simple implementation
            // In production, you might want to use a proper English number-to-words library
            return self::convertToEnglishWords($usdAmount) . ' dollars';
        } else {
            // Use existing Indonesian helper
            return \App\Helpers\NumberToWords::convertCurrency($amount);
        }
    }

    /**
     * Simple English number to words converter (basic implementation)
     *
     * @param float $number
     * @return string
     */
    private static function convertToEnglishWords(float $number): string
    {
        // This is a simplified implementation
        // For production, consider using a proper library like NumberFormatter
        $formatter = new \NumberFormatter('en', \NumberFormatter::SPELLOUT);
        $words = $formatter->format(floor($number));
        
        // Handle decimals
        $decimals = round(($number - floor($number)) * 100);
        if ($decimals > 0) {
            $words .= ' and ' . $formatter->format($decimals) . ' cents';
        }
        
        return $words;
    }
}
