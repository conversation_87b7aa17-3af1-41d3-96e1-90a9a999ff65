{"validJournal": {"transaction_date": "2024-01-15", "reference_number": "REF-VALID-001", "source_type": "Sale", "description": "Valid test journal for Cypress testing", "status": "Draft", "entries": [{"account": "<PERSON><PERSON>", "description": "Cash received from sales", "debit": 1000000, "credit": 0, "sort_order": 1}, {"account": "Penjualan", "description": "Sales revenue", "debit": 0, "credit": 1000000, "sort_order": 2}]}, "invalidJournal": {"transaction_date": "", "reference_number": "", "source_type": "", "description": "", "status": "Draft", "entries": [{"account": "", "description": "", "debit": 0, "credit": 0, "sort_order": 1}]}, "unbalancedJournal": {"transaction_date": "2024-01-15", "reference_number": "REF-UNBALANCED-001", "source_type": "Sale", "description": "Unbalanced journal for validation testing", "status": "Draft", "entries": [{"account": "<PERSON><PERSON>", "description": "Cash entry", "debit": 1000000, "credit": 0, "sort_order": 1}, {"account": "Penjualan", "description": "Sales entry", "debit": 0, "credit": 500000, "sort_order": 2}]}, "multipleEntriesJournal": {"transaction_date": "2024-01-15", "reference_number": "REF-MULTIPLE-001", "source_type": "ManualAdjust", "description": "Journal with multiple entries for testing", "status": "Draft", "entries": [{"account": "<PERSON><PERSON>", "description": "Cash entry", "debit": 1000000, "credit": 0, "sort_order": 1}, {"account": "<PERSON><PERSON><PERSON>", "description": "Receivable entry", "debit": 500000, "credit": 0, "sort_order": 2}, {"account": "Penjualan", "description": "Sales entry", "debit": 0, "credit": 1200000, "sort_order": 3}, {"account": "Beban Operasional", "description": "Operating expense", "debit": 0, "credit": 300000, "sort_order": 4}]}, "sourceTypeVariations": [{"source_type": "Sale", "description": "Sales transaction journal", "reference_prefix": "SALE"}, {"source_type": "Purchase", "description": "Purchase transaction journal", "reference_prefix": "PURCH"}, {"source_type": "Payment", "description": "Payment transaction journal", "reference_prefix": "PAY"}, {"source_type": "Receipt", "description": "Receipt transaction journal", "reference_prefix": "REC"}, {"source_type": "ManualAdjust", "description": "Manual adjustment journal", "reference_prefix": "ADJ"}], "statusVariations": [{"status": "Draft", "editable": true, "deletable": true, "badge_color": "secondary"}, {"status": "Posted", "editable": false, "deletable": false, "badge_color": "success"}, {"status": "Cancelled", "editable": false, "deletable": false, "badge_color": "danger"}, {"status": "Error", "editable": false, "deletable": false, "badge_color": "warning"}], "testAccounts": [{"kode_akun": "1101", "nama_akun": "<PERSON><PERSON>", "kategori_akun": "<PERSON><PERSON>", "tipe_akun": "Debit", "saldo_awal": 0}, {"kode_akun": "1201", "nama_akun": "<PERSON><PERSON><PERSON>", "kategori_akun": "<PERSON><PERSON>", "tipe_akun": "Debit", "saldo_awal": 0}, {"kode_akun": "4101", "nama_akun": "Penjualan", "kategori_akun": "Pendapatan", "tipe_akun": "Kredit", "saldo_awal": 0}, {"kode_akun": "5101", "nama_akun": "Beban Operasional", "kategori_akun": "<PERSON><PERSON>", "tipe_akun": "Debit", "saldo_awal": 0}, {"kode_akun": "2101", "nama_akun": "<PERSON><PERSON><PERSON>", "kategori_akun": "Kewajiban", "tipe_akun": "Kredit", "saldo_awal": 0}], "validationTestCases": [{"name": "empty_transaction_date", "data": {"transaction_date": "", "description": "Test description"}, "expectedError": "<PERSON><PERSON> trans<PERSON>i wajib diisi"}, {"name": "empty_description", "data": {"transaction_date": "2024-01-15", "description": ""}, "expectedError": "<PERSON><PERSON><PERSON><PERSON> wajib diisi"}, {"name": "invalid_date_format", "data": {"transaction_date": "invalid-date", "description": "Test description"}, "expectedError": "Format tanggal tidak valid"}, {"name": "negative_debit", "data": {"transaction_date": "2024-01-15", "description": "Test description", "entries": [{"account": "<PERSON><PERSON>", "description": "Test entry", "debit": -1000000, "credit": 0}]}, "expectedError": "Nilai debit tidak boleh negatif"}, {"name": "both_debit_credit_filled", "data": {"transaction_date": "2024-01-15", "description": "Test description", "entries": [{"account": "<PERSON><PERSON>", "description": "Test entry", "debit": 1000000, "credit": 500000}]}, "expectedError": "Tidak boleh mengisi debit dan kredit bersamaan"}, {"name": "both_debit_credit_zero", "data": {"transaction_date": "2024-01-15", "description": "Test description", "entries": [{"account": "<PERSON><PERSON>", "description": "Test entry", "debit": 0, "credit": 0}]}, "expectedError": "Debit atau kredit harus memiliki nilai"}], "searchTestData": [{"journal_number": "JRN-202401-0001", "reference_number": "SEARCH-TEST-001", "description": "First search test journal"}, {"journal_number": "JRN-202401-0002", "reference_number": "SEARCH-TEST-002", "description": "Second search test journal"}, {"journal_number": "JRN-202401-0003", "reference_number": "FILTER-TEST-001", "description": "Filter test journal"}], "paginationTestData": {"totalRecords": 25, "itemsPerPage": 10, "expectedPages": 3, "recordPrefix": "PAGINATION-TEST"}, "performanceTestData": {"largeDataset": {"recordCount": 100, "maxLoadTime": 5000, "maxRenderTime": 2000}, "complexJournal": {"entryCount": 10, "maxSaveTime": 3000}}, "accessibilityTestData": {"requiredAriaLabels": ["transaction-date", "description", "account-select", "debit-input", "credit-input"], "keyboardNavigation": ["tab-navigation", "enter-submit", "escape-cancel", "arrow-navigation"]}}